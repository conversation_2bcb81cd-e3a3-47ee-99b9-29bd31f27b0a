/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>WA Web Share API Integration
 *
 * This script provides Web Share API functionality for the PWA,
 * allowing users to share content using native sharing capabilities.
 */

// Web Share API Support
window.qPwaShare = {
    /**
     * Check if Web Share API is supported
     */
    isSupported: function() {
        return navigator.share !== undefined;
    },
    
    /**
     * Share content using Web Share API
     */
    share: function(data) {
        if (!this.isSupported()) {
            console.warn('Q-PWA: Web Share API not supported');
            return Promise.resolve(false);
        }
        
        const shareData = {
            title: data.title || document.title,
            text: data.text || '',
            url: data.url || window.location.href
        };
        
        return navigator.share(shareData)
            .then(() => {
                console.log('Q-PWA: Content shared successfully');
                return true;
            })
            .catch((error) => {
                console.error('Q-PWA: Error sharing content:', error);
                return false;
            });
    },
    
    /**
     * Share the current page
     */
    shareCurrentPage: function() {
        return this.share({
            title: document.title,
            text: document.querySelector('meta[name="description"]')?.content || '',
            url: window.location.href
        });
    },

    /**
     * Share with fallback for unsupported browsers
     */
    shareWithFallback: function(data) {
        if (this.isSupported()) {
            return this.share(data);
        } else {
            // Fallback to clipboard or other sharing methods
            return this.fallbackShare(data);
        }
    },

    /**
     * Fallback sharing method for unsupported browsers
     */
    fallbackShare: function(data) {
        const shareUrl = data.url || window.location.href;
        const shareText = data.text || '';
        const shareTitle = data.title || document.title;
        
        // Try to copy to clipboard
        if (navigator.clipboard && navigator.clipboard.writeText) {
            const textToCopy = `${shareTitle}\n${shareText}\n${shareUrl}`;
            return navigator.clipboard.writeText(textToCopy)
                .then(() => {
                    this.showFallbackNotification('Link copied to clipboard!');
                    return true;
                })
                .catch(() => {
                    this.showSocialShareModal(data);
                    return false;
                });
        } else {
            this.showSocialShareModal(data);
            return Promise.resolve(false);
        }
    },

    /**
     * Show social share modal as ultimate fallback
     */
    showSocialShareModal: function(data) {
        const shareUrl = encodeURIComponent(data.url || window.location.href);
        const shareText = encodeURIComponent(data.text || '');
        const shareTitle = encodeURIComponent(data.title || document.title);
        
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div id="q-pwa-share-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 20px; text-align: center;">
                    <h3 style="margin: 0 0 20px 0;">Share this page</h3>
                    <div style="display: flex; justify-content: center; gap: 15px; margin-bottom: 20px;">
                        <a href="https://twitter.com/intent/tweet?text=${shareTitle}&url=${shareUrl}" target="_blank" style="display: inline-block; padding: 10px; background: #1da1f2; color: white; text-decoration: none; border-radius: 5px;">Twitter</a>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=${shareUrl}" target="_blank" style="display: inline-block; padding: 10px; background: #4267B2; color: white; text-decoration: none; border-radius: 5px;">Facebook</a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}" target="_blank" style="display: inline-block; padding: 10px; background: #0077b5; color: white; text-decoration: none; border-radius: 5px;">LinkedIn</a>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <input type="text" value="${data.url || window.location.href}" readonly style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                    </div>
                    <button id="q-pwa-share-close" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Close</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Handle close button
        document.getElementById('q-pwa-share-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        // Handle click outside modal
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    },

    /**
     * Show fallback notification
     */
    showFallbackNotification: function(message) {
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; background: #4CAF50; color: white; padding: 15px; border-radius: 5px; z-index: 10000; max-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
                <p style="margin: 0; font-size: 14px;">${message}</p>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
};

// Initialize Web Share functionality when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Find all elements with share functionality
    const shareButtons = document.querySelectorAll('.q-pwa-share, [data-q-share]');
    
    shareButtons.forEach(button => {
        // Show share buttons if Web Share API is supported or fallback is available
        button.style.display = 'inline-block';
        
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const shareData = {
                title: this.dataset.shareTitle || this.getAttribute('data-title') || document.title,
                text: this.dataset.shareText || this.getAttribute('data-text') || '',
                url: this.dataset.shareUrl || this.getAttribute('data-url') || window.location.href
            };
            
            window.qPwaShare.shareWithFallback(shareData);
        });
    });

    // Add share functionality to existing share buttons
    const existingShareButtons = document.querySelectorAll('a[href*="twitter.com/intent"], a[href*="facebook.com/sharer"], .share-button, .social-share');
    
    existingShareButtons.forEach(button => {
        // Only enhance if Web Share API is supported
        if (window.qPwaShare.isSupported()) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Extract sharing data from the button
                const href = this.getAttribute('href');
                let shareData = {
                    title: document.title,
                    text: '',
                    url: window.location.href
                };
                
                // Try to extract data from href for Twitter
                if (href && href.includes('twitter.com/intent')) {
                    const urlParams = new URLSearchParams(href.split('?')[1]);
                    shareData.text = urlParams.get('text') || '';
                    shareData.url = urlParams.get('url') || window.location.href;
                }
                
                // Try to extract data from href for Facebook
                if (href && href.includes('facebook.com/sharer')) {
                    const urlParams = new URLSearchParams(href.split('?')[1]);
                    shareData.url = urlParams.get('u') || window.location.href;
                }
                
                window.qPwaShare.share(shareData);
            });
        }
    });
});

// Global function for manual sharing
window.qPwaSharePage = function(title, text, url) {
    return window.qPwaShare.shareWithFallback({
        title: title || document.title,
        text: text || '',
        url: url || window.location.href
    });
};
