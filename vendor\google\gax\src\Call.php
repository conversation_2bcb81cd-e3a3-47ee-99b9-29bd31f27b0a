<?php
/*
 * Copyright 2018 Google LLC
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *     * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above
 * copyright notice, this list of conditions and the following disclaimer
 * in the documentation and/or other materials provided with the
 * distribution.
 *     * Neither the name of Google Inc. nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, <PERSON><PERSON><PERSON><PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

namespace Google\ApiCore;

use Google\Protobuf\Internal\Message;

/**
 * Contains information necessary to manage a network request.
 */
class Call
{
    const UNARY_CALL = 0;
    const BIDI_STREAMING_CALL = 1;
    const CLIENT_STREAMING_CALL = 2;
    const SERVER_STREAMING_CALL = 3;
    const LONGRUNNING_CALL = 4;
    const PAGINATED_CALL = 5;

    private $method;
    private $callType;
    private $decodeType;
    private $message;
    private $descriptor;

    /**
     * @param string $method
     * @param string $decodeType
     * @param mixed|Message $message
     * @param array|null $descriptor
     * @param int $callType
     */
    public function __construct(
        string $method,
        ?string $decodeType = null,
        $message = null,
        $descriptor = [],
        int $callType = Call::UNARY_CALL
    ) {
        $this->method = $method;
        $this->decodeType = $decodeType;
        $this->message = $message;
        $this->descriptor = $descriptor;
        $this->callType = $callType;
    }

    /**
     * @return string
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * @return int
     */
    public function getCallType()
    {
        return $this->callType;
    }

    /**
     * @return string
     */
    public function getDecodeType()
    {
        return $this->decodeType;
    }

    /**
     * @return mixed|Message
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * @return array|null
     */
    public function getDescriptor()
    {
        return $this->descriptor;
    }

    /**
     * @param mixed|Message $message
     * @return Call
     */
    public function withMessage($message)
    {
        // @phpstan-ignore-next-line
        return new static(
            $this->method,
            $this->decodeType,
            $message,
            $this->descriptor,
            $this->callType
        );
    }
}
