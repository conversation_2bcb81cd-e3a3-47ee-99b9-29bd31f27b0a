<?php

/**
 * Plugin Name: Q-Pusher
 * Description: Q-<PERSON>usher is a powerful plugin that seamlessly integrates with Formidable Forms, allowing you to send push notifications on form submissions.
 * With Q-Pusher, you can stay updated and connected with your users in real-time.
 * Version: 1.0.0
 * Author: Q-Ai
 */

if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    add_action('admin_notices', function () {
        echo '<div class="error"><p>Q Pusher requires PHP 8.1 or higher. Your PHP version is ' . PHP_VERSION . '</p></div>';
    });
    return;
}

defined('ABSPATH') or die('No script kiddies please!');

// Temporarily suppress specific deprecation notices
error_reporting(E_ALL & ~E_DEPRECATED);

// Define constants for plugin directory and URL
define('Q_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('Q_PLUGIN_URL', plugin_dir_url(__FILE__));
define('Q_DB_VERSION', '1.1');

// Include required files
require_once Q_PLUGIN_DIR . 'includes/class-q-diagnostics.php';
require_once Q_PLUGIN_DIR . 'includes/notification-analytics.php';
require_once Q_PLUGIN_DIR . 'includes/firebase-functions.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-activator.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-settings.php';
require_once Q_PLUGIN_DIR . 'includes/pwa/class-q-pwa.php';
require_once Q_PLUGIN_DIR . 'includes/pwa/class-q-pwa-settings.php';

// Initialize settings
add_action('init', ['Q_Settings', 'init']);

// Initialize PWA functionality
add_action('init', ['Q_PWA', 'init']);
add_action('init', ['Q_PWA_Settings', 'init']);
add_action('init', ['Q_PWA', 'track_pwa_usage']);

// Flush rewrite rules once to ensure PWA routes are registered
register_activation_hook(__FILE__, function () {
    update_option('q_pwa_flush_rewrite_rules', true);
});

// Flush rewrite rules on plugin update
add_action('upgrader_process_complete', function ($upgrader, $options) {
    if ($options['action'] == 'update' && $options['type'] == 'plugin') {
        foreach ($options['plugins'] as $plugin) {
            if ($plugin == plugin_basename(__FILE__)) {
                update_option('q_pwa_flush_rewrite_rules', true);
                break;
            }
        }
    }
}, 10, 2);

// Change the conditional include to use proper hook
/**
 * Load Formidable Forms dependencies if the class exists.
 */
add_action('plugins_loaded', 'q_load_formidable_dependencies', 20);

/**
 * Load Formidable Forms dependencies if the class exists.
 */
function q_load_formidable_dependencies()
{
    if (class_exists('FrmFormAction')) {
        // Include Formidable actions if the class is available
        require_once Q_PLUGIN_DIR . 'includes/formidable-actions.php';
    } else {
        // Show admin notice if Formidable Forms is not active
        add_action('admin_notices', 'q_formidable_missing_notice');
    }
}

/**
 * Activation hook to set up the plugin.
 */
register_activation_hook(__FILE__, function () {
    Q_Activator::activate();
    q_activate();
});

/**
 * Create the Q-Notify form and copy the service workers on activation.
 */
function q_activate()
{
    q_create_notify_form();
    q_copy_service_worker();

    // Create PWA files if PWA is enabled
    if (get_option('q_pwa_enabled', false) && class_exists('Q_PWA')) {
        Q_PWA::create_physical_manifest_file();
        Q_PWA::create_physical_service_worker_file();

        // Create physical offline.html file
        q_create_physical_offline_file();
    }

    // Set flag to flush rewrite rules for PWA
    update_option('q_pwa_flush_rewrite_rules', true);

    // Set default PWA settings if not already set
    if (get_option('q_pwa_app_name') === false) {
        update_option('q_pwa_app_name', get_bloginfo('name'));
    }

    if (get_option('q_pwa_short_name') === false) {
        update_option('q_pwa_short_name', get_bloginfo('name'));
    }

    if (get_option('q_pwa_description') === false) {
        update_option('q_pwa_description', get_bloginfo('description'));
    }

    if (get_option('q_pwa_theme_color') === false) {
        update_option('q_pwa_theme_color', '#ffffff');
    }

    if (get_option('q_pwa_background_color') === false) {
        update_option('q_pwa_background_color', '#ffffff');
    }

    if (get_option('q_pwa_display') === false) {
        update_option('q_pwa_display', 'standalone');
    }

    // Always update cache version to ensure service worker is refreshed
    update_option('q_pwa_cache_version', '1.0.' . time());
}

/**
 * Create the Q-Notify form if it doesn't exist.
 */
function q_create_notify_form()
{
    if (class_exists('FrmForm')) {
        global $wpdb;
        $form_name = 'Q-Notify';
        $form_exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$wpdb->prefix}frm_forms WHERE name = %s", $form_name));

        if (!$form_exists) {
            // Create form with specified values
            $form_values = array(
                'name' => $form_name,
                'description' => 'Form for sending notifications',
                'status' => 'published',
                'is_template' => 0,
                'form_key' => uniqid('qnotify_') // Add unique key
            );
            $form_id = FrmForm::create($form_values);

            if ($form_id) {
                q_add_fields_to_form($form_id);
            }
        }
    }
}

/**
 * Add fields to the Q-Notify form.
 */
function q_add_fields_to_form($form_id)
{
    $fields = array(
        array(
            'name' => 'Notification Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter the type of notification (e.g., Alert, Update, News, etc.)'
        ),
        array(
            'name' => 'Audience Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Specify the type of audience (e.g., All Users, Subscribers, Admins, etc.)'
        ),
        array(
            'name' => 'Audience',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter audience emails'
        )
    );

    // Add each field to the form
    foreach ($fields as $field) {
        FrmField::create($field);
    }
}

/**
 * Copy the service worker to the root directory.
 */
function q_copy_service_worker()
{
    $sw_source = Q_PLUGIN_DIR . 'firebase-messaging-sw.js';
    $sw_dest = ABSPATH . 'firebase-messaging-sw.js';

    // Debug logging
    error_log('Q-Pusher: Attempting to copy service worker');
    error_log('Q-Pusher: Source path: ' . $sw_source);
    error_log('Q-Pusher: Destination path: ' . $sw_dest);

    // Check if the source file exists
    if (!file_exists($sw_source)) {
        error_log('Q-Pusher: Service worker source file not found at ' . $sw_source);
        return false;
    }

    // Also create the PWA service worker file
    $pwa_sw_dest = ABSPATH . 'q-sw.js';
    error_log('Q-Pusher: PWA service worker destination path: ' . $pwa_sw_dest);

    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id', 'q-push-50a80'), // Default to known project ID
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id')
        );
    }

    // Ensure projectId is set
    if (empty($firebase_config['projectId'])) {
        $firebase_config['projectId'] = 'q-push-50a80'; // Default to known project ID
    }

    // Verify Firebase configuration
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            error_log("Q-Pusher: Missing Firebase configuration: {$field}");
            return false;
        }
    }

    try {
        // Read the service worker template
        $sw_content = file_get_contents($sw_source);
        if ($sw_content === false) {
            throw new Exception('Failed to read service worker source file');
        }

        // Replace placeholders with actual values
        $sw_content = str_replace(
            [
                'FIREBASE_API_KEY',
                'FIREBASE_AUTH_DOMAIN',
                'FIREBASE_PROJECT_ID',
                'FIREBASE_STORAGE_BUCKET',
                'FIREBASE_MESSAGING_SENDER_ID',
                'FIREBASE_APP_ID'
            ],
            [
                $firebase_config['apiKey'],
                $firebase_config['authDomain'],
                $firebase_config['projectId'],
                $firebase_config['storageBucket'],
                $firebase_config['messagingSenderId'],
                $firebase_config['appId']
            ],
            $sw_content
        );

        // Ensure WordPress root directory is writable
        if (!is_writable(ABSPATH)) {
            throw new Exception('WordPress root directory is not writable');
        }

        // Delete existing service worker if it exists
        if (file_exists($sw_dest) && !unlink($sw_dest)) {
            throw new Exception('Failed to delete existing service worker file');
        }

        // Write the configured service worker
        if (file_put_contents($sw_dest, $sw_content) === false) {
            throw new Exception('Failed to write service worker file');
        }

        // Set proper permissions
        if (!chmod($sw_dest, 0644)) {
            throw new Exception('Failed to set service worker file permissions');
        }

        // Add rewrite rules
        add_rewrite_rule(
            '^firebase-messaging-sw\.js$',
            'index.php?q_service_worker=1',
            'top'
        );

        // Generate and write the PWA service worker if PWA is enabled
        if (get_option('q_pwa_enabled', false)) {
            try {
                // Generate PWA service worker content
                $pwa_sw_content = Q_PWA::generate_service_worker();

                // Write the PWA service worker
                if (file_put_contents($pwa_sw_dest, $pwa_sw_content) === false) {
                    throw new Exception('Failed to write PWA service worker file');
                }

                // Set proper permissions
                if (!chmod($pwa_sw_dest, 0644)) {
                    throw new Exception('Failed to set PWA service worker file permissions');
                }

                error_log('Q-Pusher: PWA service worker successfully generated and configured');
            } catch (Exception $e) {
                error_log('Q-Pusher Error: ' . $e->getMessage());
                // Continue even if PWA service worker fails
            }
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        error_log('Q-Pusher: Service worker successfully copied and configured');
        return true;

    } catch (Exception $e) {
        error_log('Q-Pusher Error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Deactivation hook to clean up on plugin deactivation.
 */
register_deactivation_hook(__FILE__, 'q_deactivate');

/**
 * Remove the service worker files on deactivation.
 */
function q_deactivate()
{
    // Remove Firebase service worker file
    $firebase_sw_file = ABSPATH . 'firebase-messaging-sw.js';
    if (file_exists($firebase_sw_file)) {
        unlink($firebase_sw_file);
    }

    // Remove PWA service worker file
    $pwa_sw_file = ABSPATH . 'q-sw.js';
    if (file_exists($pwa_sw_file)) {
        unlink($pwa_sw_file);
    }

    // Flush rewrite rules to remove PWA routes
    flush_rewrite_rules();
}

/**
 * Enqueue scripts and styles for the frontend.
 */
add_action('wp_enqueue_scripts', 'q_enqueue_scripts');

/**
 * Enqueue scripts and styles for the frontend.
 */
function q_enqueue_scripts()
{
    q_enqueue_firebase_scripts();
    q_enqueue_custom_scripts();
}

/**
 * Enqueue Firebase SDK scripts.
 */
function q_enqueue_firebase_scripts()
{
    // Firebase SDK is now imported as a module in firebase-init.js
}

/**
 * Enqueue custom scripts for the plugin.
 */
function q_enqueue_custom_scripts()
{
    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id', 'q-push-50a80'), // Default to known project ID
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id'),
            'measurementId' => get_option('q_firebase_measurement_id'),
            'publicVapidKey' => get_option('q_firebase_public_vapid_key'),
        );
    }

    // Ensure projectId is set
    if (empty($firebase_config['projectId'])) {
        $firebase_config['projectId'] = 'q-push-50a80'; // Default to known project ID
    }

    // Verify required fields
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    $missing_fields = array();

    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        error_log('Q-Pusher: Missing required Firebase configuration fields: ' . implode(', ', $missing_fields));
    }

    // Add config as a global variable
    wp_register_script('q-firebase-config', '', array(), '', true);
    wp_enqueue_script('q-firebase-config');
    wp_add_inline_script(
        'q-firebase-config',
        'window.q_firebase_config = ' . json_encode($firebase_config) . ';',
        'before'
    );

    // Suppress jQuery migrate warnings
    wp_add_inline_script('jquery-migrate', 'jQuery.migrateMute = true;', 'before');

    // Add type="module" to script tags
    add_filter('script_loader_tag', function ($tag, $handle, $src) {
        if (in_array($handle, ['q-firebase-init', 'q-subscription', 'q-notification-badge', 'q-pwa-register'])) {
            $tag = '<script type="module" src="' . esc_url($src) . '"></script>';
        }
        return $tag;
    }, 10, 3);

    // Enqueue the module scripts
    wp_enqueue_script(
        'q-firebase-init',
        Q_PLUGIN_URL . 'includes/js/firebase-init.js',
        array('q-firebase-config'),
        '1.0.0',
        true
    );

    wp_enqueue_script(
        'q-subscription',
        Q_PLUGIN_URL . 'includes/js/subscription.js',
        array('q-firebase-init', 'jquery'),
        '1.0.0',
        true
    );

    // Enqueue the notification badge script
    wp_enqueue_script(
        'q-notification-badge',
        Q_PLUGIN_URL . 'includes/js/notification-badge.js',
        array('q-firebase-init'),
        '1.0.0',
        true
    );

    // Pass AJAX URL and nonce to JavaScript
    wp_localize_script('q-subscription', 'q_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('q_subscribe_nonce')
    ));
}



/**
 * Add headers for the service worker to allow it to be served correctly.
 */
add_action('init', 'q_add_service_worker_headers');

/**
 * Add headers for service worker and handle requests
 */
function q_add_service_worker_headers()
{
    if (isset($_GET['q_service_worker'])) {
        header('Content-Type: application/javascript');
        header('Service-Worker-Allowed: /');
        header('Cache-Control: no-cache');

        $sw_path = ABSPATH . 'firebase-messaging-sw.js';
        if (file_exists($sw_path)) {
            readfile($sw_path);
        } else {
            error_log('Q-Pusher: Service worker file not found at: ' . $sw_path);
            status_header(404);
        }
        exit;
    }
}
add_action('init', 'q_add_service_worker_headers');

/**
 * Add query vars for service workers and PWA
 */
function q_add_query_vars($vars)
{
    $vars[] = 'q_service_worker';
    $vars[] = 'q_pwa_sw';
    $vars[] = 'q_manifest';
    $vars[] = 'q_offline';
    return $vars;
}
add_filter('query_vars', 'q_add_query_vars');

/**
 * Shortcode to display a subscription button for notifications.
 */
add_shortcode('q_subscribe_button', 'q_subscribe_button_shortcode');

/**
 * Shortcode to display a subscription button for notifications.
 */
function q_subscribe_button_shortcode()
{
    ob_start();
    ?>
    <button id="q-subscribe-button" class="ui-button primary"><i class="bi bi-bell-fill"></i> Subscribe to
        Notifications</button>
    <?php
    return ob_get_clean();
}

/**
 * Shortcode to display an "Add to Home Screen" button for PWA.
 */
add_shortcode('q_add_to_home', 'q_add_to_home_shortcode');

/**
 * Shortcode to display an "Add to Home Screen" button for PWA.
 */
function q_add_to_home_shortcode($atts)
{
    // Parse attributes
    $atts = shortcode_atts(
        array(
            'text' => 'Add to Home Screen',
            'class' => 'ui-button primary',
            'icon' => true,
            'style' => 'default', // default, pill, flat, outline
            'color' => '',        // Custom color (hex code)
            'show_instructions' => true, // Show platform-specific instructions
            'size' => 'medium',   // small, medium, large
            'full_width' => false, // Make button full width
            'align' => 'left',    // left, center, right
            'custom_id' => '',    // Custom ID for the button
        ),
        $atts,
        'q_add_to_home'
    );

    // Only show if PWA is enabled
    if (!get_option('q_pwa_enabled', false)) {
        return '';
    }

    // Generate unique ID if not provided
    $button_id = !empty($atts['custom_id']) ? $atts['custom_id'] : 'q-add-to-home-button-' . uniqid();

    // Set icon based on attribute
    $icon_html = '';
    if ($atts['icon'] === true || $atts['icon'] === 'true' || $atts['icon'] === '1') {
        $icon_html = '<i class="bi bi-download"></i> ';
    } elseif ($atts['icon'] !== false && $atts['icon'] !== 'false' && $atts['icon'] !== '0') {
        // If icon is a custom value, use it as a custom icon class
        $icon_html = '<i class="' . esc_attr($atts['icon']) . '"></i> ';
    }

    // Build inline styles
    $inline_styles = '';

    // Button style
    switch ($atts['style']) {
        case 'pill':
            $inline_styles .= 'border-radius: 50px; ';
            break;
        case 'flat':
            $inline_styles .= 'border-radius: 0; box-shadow: none; ';
            break;
        case 'outline':
            $inline_styles .= 'background: transparent; border: 2px solid currentColor; ';
            break;
    }

    // Button size
    switch ($atts['size']) {
        case 'small':
            $inline_styles .= 'font-size: 0.85em; padding: 0.3em 0.8em; ';
            break;
        case 'large':
            $inline_styles .= 'font-size: 1.2em; padding: 0.6em 1.2em; ';
            break;
    }

    // Custom color
    if (!empty($atts['color'])) {
        if ($atts['style'] === 'outline') {
            $inline_styles .= 'color: ' . esc_attr($atts['color']) . '; border-color: ' . esc_attr($atts['color']) . '; ';
        } else {
            $inline_styles .= 'background-color: ' . esc_attr($atts['color']) . '; ';
        }
    }

    // Full width
    if ($atts['full_width'] === true || $atts['full_width'] === 'true' || $atts['full_width'] === '1') {
        $inline_styles .= 'width: 100%; display: block; ';
    }

    // Alignment wrapper
    $alignment_style = '';
    if ($atts['align'] === 'center') {
        $alignment_style = 'text-align: center; ';
    } elseif ($atts['align'] === 'right') {
        $alignment_style = 'text-align: right; ';
    }

    // Generate button HTML
    $button_html = sprintf(
        '<div style="%s"><button id="%s" class="%s" style="%s">%s%s</button></div>',
        esc_attr($alignment_style),
        esc_attr($button_id),
        esc_attr($atts['class']),
        esc_attr($inline_styles),
        $icon_html,
        esc_html($atts['text'])
    );

    // Add inline script to handle the button click
    $show_instructions = $atts['show_instructions'] === true || $atts['show_instructions'] === 'true' || $atts['show_instructions'] === '1';

    $script = "
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const addToHomeButton = document.getElementById('" . esc_js($button_id) . "');
        if (addToHomeButton) {
            // Detect platform
            let detectedPlatform = 'unknown';
            const ua = navigator.userAgent.toLowerCase();

            if (/iphone|ipad|ipod/.test(ua)) {
                detectedPlatform = 'ios';
            } else if (/android/.test(ua)) {
                detectedPlatform = 'android';
            } else {
                detectedPlatform = 'desktop';
            }

            // Check if already in standalone mode - hide button if app is already installed
            if (window.matchMedia('(display-mode: standalone)').matches ||
                window.navigator.standalone ||
                document.referrer.includes('android-app://')) {
                addToHomeButton.style.display = 'none';
            } else {
                // Show the button by default - it's not in standalone mode
                addToHomeButton.style.display = 'inline-block';
            }

            // Variable to store the install prompt event
            let deferredPrompt = null;

            // Listen for the beforeinstallprompt event
            window.addEventListener('beforeinstallprompt', (e) => {
                // Prevent the default browser install prompt
                e.preventDefault();

                // Store the event for later use
                deferredPrompt = e;

                // Make sure the button is visible
                addToHomeButton.style.display = 'inline-block';

                // Handle button click for non-iOS platforms
                if (detectedPlatform !== 'ios') {
                    addToHomeButton.addEventListener('click', handleNonIOSInstall);
                }
            });

            // For iOS, set up the click handler immediately
            if (detectedPlatform === 'ios') {
                // Only show if not already in standalone mode
                if (!window.navigator.standalone) {
                    addToHomeButton.style.display = 'inline-block';

                    // Handle button click for iOS
                    addToHomeButton.addEventListener('click', () => {
                        if (" . ($show_instructions ? 'true' : 'false') . ") {
                            showPlatformInstructions('ios');
                        }
                    });
                }
            }

            // Function to handle installation for non-iOS platforms
            function handleNonIOSInstall() {
                // If no prompt is available, show instructions
                if (!deferredPrompt) {
                    if (" . ($show_instructions ? 'true' : 'false') . ") {
                        showPlatformInstructions(detectedPlatform);
                    }
                    return;
                }

                // Show the install prompt
                deferredPrompt.prompt();

                // Wait for the user to respond to the prompt
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                        // Hide the button after installation
                        addToHomeButton.style.display = 'none';
                    } else if (" . ($show_instructions ? 'true' : 'false') . ") {
                        // If user dismissed but we want to show instructions
                        showPlatformInstructions(detectedPlatform);
                    }
                    // Clear the deferred prompt variable
                    deferredPrompt = null;
                });
            }

            // Add click handler for non-iOS platforms
            if (detectedPlatform !== 'ios') {
                addToHomeButton.addEventListener('click', function() {
                    if (deferredPrompt) {
                        handleNonIOSInstall();
                    } else {
                        // If no prompt is available, show instructions
                        if (" . ($show_instructions ? 'true' : 'false') . ") {
                            showPlatformInstructions(detectedPlatform);
                        }
                    }
                });
            }

            // Hide button if app is installed during the session
            window.addEventListener('appinstalled', () => {
                addToHomeButton.style.display = 'none';
            });

            // Function to show platform-specific instructions
            function showPlatformInstructions(platform) {
                // Get instructions based on platform
                let instructions = '';
                let title = 'Installation Instructions';

                switch (platform) {
                    case 'ios':
                        instructions = qPwaData.iosInstructions || 'To install this app on your iOS device:\\n1. Tap the Share icon (rectangle with arrow) at the bottom of the screen\\n2. Scroll down and tap \\'Add to Home Screen\\'\\n3. Tap \\'Add\\' in the top right corner';
                        title = 'Install on iOS';
                        break;
                    case 'android':
                        instructions = qPwaData.androidInstructions || 'To install this app on your Android device:\\n1. Tap the \\'Install\\' button in the prompt\\n2. If no prompt appears, tap the menu icon (three dots) in your browser\\n3. Select \\'Add to Home screen\\' or \\'Install App\\'';
                        title = 'Install on Android';
                        break;
                    case 'desktop':
                        instructions = qPwaData.desktopInstructions || 'To install this app on your desktop:\\n1. Click the install icon in the address bar\\n2. Click \\'Install\\' in the prompt that appears\\n3. The app will open in a new window';
                        title = 'Install on Desktop';
                        break;
                    default:
                        return; // Don't show instructions for unknown platforms
                }

                // Format instructions with line breaks
                const formattedInstructions = instructions.replace(/\\n/g, '<br>');

                // Create modal if it doesn't exist
                let modal = document.getElementById('q-pwa-instructions-modal');
                if (modal) {
                    modal.remove();
                }

                modal = document.createElement('div');
                modal.id = 'q-pwa-instructions-modal';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.zIndex = '10000';
                modal.style.display = 'flex';
                modal.style.alignItems = 'center';
                modal.style.justifyContent = 'center';

                // Create modal content
                const modalContent = document.createElement('div');
                modalContent.style.backgroundColor = 'white';
                modalContent.style.borderRadius = '8px';
                modalContent.style.padding = '20px';
                modalContent.style.maxWidth = '500px';
                modalContent.style.width = '90%';
                modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';

                // Add platform icon
                let platformIcon = '';
                switch (platform) {
                    case 'ios':
                        platformIcon = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 2a8 8 0 0 0-8 8v12h16V10a8 8 0 0 0-8-8z\"></path><path d=\"M4 10v2a8 8 0 0 0 16 0v-2\"></path><path d=\"M8 16v2\"></path><path d=\"M16 16v2\"></path></svg>';
                        break;
                    case 'android':
                        platformIcon = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M5 16V8a7 7 0 0 1 14 0v8\"></path><path d=\"M9 18h6\"></path><path d=\"M3 16h18\"></path><path d=\"M12 8v2\"></path><path d=\"M8 2v2\"></path><path d=\"M16 2v2\"></path></svg>';
                        break;
                    default:
                        platformIcon = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect><line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line><line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line></svg>';
                }

                // Set modal content
                modalContent.innerHTML = `
                    <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">
                        <div style=\"margin-right: 15px; color: #0384c6;\">${platformIcon}</div>
                        <h2 style=\"margin: 0; font-size: 20px;\">${title}</h2>
                    </div>
                    <div style=\"margin-bottom: 20px; line-height: 1.5;\">${formattedInstructions}</div>
                    <div style=\"text-align: right;\">
                        <button id=\"q-pwa-instructions-close\" style=\"background-color: #0384c6; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;\">
                            Got it
                        </button>
                    </div>
                `;

                // Add modal to document
                modal.appendChild(modalContent);
                document.body.appendChild(modal);

                // Add event listener to close button
                document.getElementById('q-pwa-instructions-close').addEventListener('click', () => {
                    modal.remove();
                });

                // Close modal when clicking outside
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
            }
        }
    });
    </script>
    ";

    return $button_html . $script;
}



/**
 * Handle AJAX request to save the subscription token.
 */
add_action('wp_ajax_q_subscribe', 'q_subscribe_callback');
add_action('wp_ajax_nopriv_q_subscribe', 'q_subscribe_callback'); // For non-logged-in users

/**
 * Handle AJAX request to save the subscription token.
 */
function q_subscribe_callback()
{
    try {
        // Verify nonce checks
        if (!check_ajax_referer('q_subscribe_nonce', 'nonce', false)) {
            wp_send_json_error([
                'message' => 'Invalid security token'
            ]);
            return;
        }

        if (!check_ajax_referer('q_subscribe_nonce', 'security', false)) {
            wp_send_json_error([
                'message' => 'Secondary security check failed'
            ]);
            return;
        }

        // Validate token presence
        if (empty($_POST['token'])) {
            wp_send_json_error([
                'message' => 'Token is required'
            ]);
            return;
        }

        $token = sanitize_text_field($_POST['token']);

        // Validate token format
        if (strlen($token) < 50) {
            wp_send_json_error([
                'message' => 'Invalid token format'
            ]);
            return;
        }

        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error([
                'message' => 'User must be logged in'
            ]);
            return;
        }

        // Store the token
        $result = update_user_meta($user_id, 'q_push_token', $token);

        if ($result === false) {
            wp_send_json_error([
                'message' => 'Failed to save subscription'
            ]);
            return;
        }

        wp_send_json_success([
            'message' => 'Subscription successful!',
            'userId' => $user_id
        ]);

    } catch (Exception $e) {
        error_log('Subscription error: ' . $e->getMessage());
        wp_send_json_error([
            'message' => 'An unexpected error occurred'
        ]);
    }
}

/**
 * Display an admin notice if Formidable Forms is missing.
 */
function q_formidable_missing_notice()
{
    ?>
    <div class="notice notice-error is-dismissible">
        <p><?php _e('Q Pusher plugin requires Formidable Forms to be installed and activated.', 'formidable-firebase-push'); ?>
        </p>
    </div>
    <?php
}

// Add admin menu
add_action('admin_menu', 'q_add_admin_menu');

function q_add_admin_menu()
{
    add_menu_page(
        __('Q-Notify Subscribers', 'formidable-firebase-push'),
        __('Q-Notify', 'formidable-firebase-push'),
        'manage_options',
        'q-notify-subscribers',
        'q_render_subscribers_page',
        'dashicons-bell',
        30
    );
}

function q_render_subscribers_page()
{
    // Check user capabilities
    if (!current_user_can('manage_options')) {
        return;
    }

    // Get all subscribers
    $subscribers = get_users(array(
        'meta_key' => 'q_push_token',
        'meta_value' => '',
        'meta_compare' => '!='
    ));

    // Get analytics data
    $total_notifications = q_get_total_notifications();
    $engagement_rate = q_get_engagement_rate();

    // Include the template
    require_once Q_PLUGIN_DIR . 'includes/templates/subscribers-page.php';
}

// Add AJAX handler for removing subscribers
add_action('wp_ajax_q_remove_subscriber', 'q_remove_subscriber_callback');

function q_remove_subscriber_callback()
{
    check_ajax_referer('remove_subscriber_' . $_POST['user_id'], 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    $user_id = intval($_POST['user_id']);

    // Delete the push token
    $result = delete_user_meta($user_id, 'q_push_token');

    if ($result) {
        // Invalidate the token on Firebase if possible
        try {
            if (class_exists('Q_Firebase_Manager')) {
                Q_Firebase_Manager::invalidate_token($user_id);
            }
        } catch (Exception $e) {
            error_log('Failed to invalidate Firebase token: ' . $e->getMessage());
        }

        wp_send_json_success(__('Subscriber removed successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to remove subscriber.', 'formidable-firebase-push'));
    }
}

// Add AJAX handler for sending test notifications
add_action('wp_ajax_q_send_test_notification', 'q_send_test_notification_callback');

function q_send_test_notification_callback()
{
    // Verify nonce
    $user_id = intval($_POST['user_id']);
    check_ajax_referer('test_notification_' . $user_id, 'nonce');

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    // Get the user's push token
    $token = get_user_meta($user_id, 'q_push_token', true);

    if (empty($token)) {
        wp_send_json_error(__('No push token found for this user.', 'formidable-firebase-push'));
        return;
    }

    // Send test notification
    $title = __('Test Notification', 'formidable-firebase-push');
    $message = __('This is a test notification from Q-Pusher.', 'formidable-firebase-push');

    $result = q_send_push_notification(
        $token,
        $title,
        $message,
        '', // No image
        true // Debug mode
    );

    if ($result) {
        wp_send_json_success(__('Test notification sent successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to send test notification.', 'formidable-firebase-push'));
    }
}

// Add AJAX handler for clearing analytics
add_action('wp_ajax_q_clear_analytics', 'q_clear_analytics_callback');

function q_clear_analytics_callback()
{
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    // Verify nonce
    check_ajax_referer('q_clear_analytics', 'nonce');

    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    // Clear the analytics table
    $result = $wpdb->query("TRUNCATE TABLE $table_name");

    if ($result !== false) {
        wp_send_json_success(__('Analytics data cleared successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to clear analytics data.', 'formidable-firebase-push'));
    }
}

// Helper functions for analytics
function q_get_total_notifications()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    $count = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'sent'
    ");

    return $count ? $count : 0;
}

function q_get_engagement_rate()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    $total_sent = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'sent'
    ");

    $total_clicks = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'click'
    ");

    if (!$total_sent)
        return 0;
    return round(($total_clicks / $total_sent) * 100, 1);
}

function q_update_db_check()
{
    if (get_option('q_db_version') != Q_DB_VERSION) {
        Q_Activator::activate();
        update_option('q_db_version', Q_DB_VERSION);
    }
}
add_action('plugins_loaded', 'q_update_db_check');

add_action('admin_menu', function () {
    if (class_exists('Q_Diagnostics')) {
        add_submenu_page(
            'tools.php',
            'Q Pusher Diagnostics',
            'Q Pusher Diagnostics',
            'manage_options',
            'q-pusher-diagnostics',
            ['Q_Diagnostics', 'display_diagnostics_page']
        );
    }
});

/**
 * Verify service worker installation
 */
function q_verify_service_worker()
{
    $firebase_sw_path = ABSPATH . 'firebase-messaging-sw.js';
    $pwa_sw_path = ABSPATH . 'q-sw.js';
    $manifest_path = ABSPATH . 'q-manifest.json';

    $missing_files = [];

    if (!file_exists($firebase_sw_path)) {
        $missing_files[] = 'Firebase service worker';
    }

    if (get_option('q_pwa_enabled', false)) {
        if (!file_exists($pwa_sw_path)) {
            $missing_files[] = 'PWA service worker';
        }

        if (!file_exists($manifest_path)) {
            $missing_files[] = 'PWA manifest';
        }
    }

    if (!empty($missing_files)) {
        error_log('Q-Pusher: Missing files: ' . implode(', ', $missing_files) . '. Attempting to reinstall.');

        // Reinstall Firebase service worker
        q_copy_service_worker();

        // Reinstall PWA files if PWA is enabled
        if (get_option('q_pwa_enabled', false) && class_exists('Q_PWA')) {
            Q_PWA::create_physical_manifest_file();
            Q_PWA::create_physical_service_worker_file();
        }

        return false;
    }

    return true;
}
add_action('admin_init', 'q_verify_service_worker');

/**
 * Display admin notice for service worker issues
 */
function q_service_worker_admin_notice()
{
    if (!file_exists(ABSPATH . 'firebase-messaging-sw.js')) {
        // Add inline script for AJAX reinstall
        ?>
        <div class="error">
            <p>
                Q-Pusher: The Firebase service worker is not properly installed.
                <a href="<?php echo admin_url('tools.php?page=q-pusher-diagnostics'); ?>">
                    Run diagnostics
                </a>
                or
                <a href="#" id="q-reinstall-service-worker">
                    click here to reinstall
                </a>.
            </p>
            <div id="q-reinstall-result"
                style="display:none; margin-top: 10px; padding: 10px; background-color: #f8f8f8; border-left: 4px solid #46b450;">
            </div>
        </div>
        <script>
            jQuery(document).ready(function ($) {
                $('#q-reinstall-service-worker').on('click', function (e) {
                    e.preventDefault();

                    var $button = $(this);
                    var $result = $('#q-reinstall-result');

                    $button.text('Reinstalling...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'q_reinstall_service_worker',
                            nonce: '<?php echo wp_create_nonce('q_admin_nonce'); ?>'
                        },
                        success: function (response) {
                            if (response.success) {
                                $result.css('border-left-color', '#46b450').html('Service worker reinstalled successfully! Refresh the page to verify.');
                            } else {
                                $result.css('border-left-color', '#dc3232').html('Failed to reinstall service worker. Please check error logs or try the diagnostics page.');
                            }
                            $result.show();
                            $button.text('Click here to reinstall');
                        },
                        error: function () {
                            $result.css('border-left-color', '#dc3232').html('AJAX error occurred. Please try the diagnostics page instead.');
                            $result.show();
                            $button.text('Click here to reinstall');
                        }
                    });
                });
            });
        </script>
        <?php
    }
}
add_action('admin_notices', 'q_service_worker_admin_notice');

/**
 * Add AJAX handler for service worker reinstallation
 */
function q_handle_reinstall_service_worker()
{
    check_ajax_referer('q_admin_nonce', 'nonce');
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
        return;
    }

    // Update cache version to force refresh
    update_option('q_pwa_cache_version', '1.0.' . time());

    // Regenerate service workers
    $result = q_copy_service_worker();

    // Regenerate PWA files if PWA is enabled
    if (get_option('q_pwa_enabled', false) && class_exists('Q_PWA')) {
        Q_PWA::create_physical_manifest_file();
        Q_PWA::create_physical_service_worker_file();

        // Create physical offline.html file
        q_create_physical_offline_file();
    }

    // Flush rewrite rules to ensure routes are registered
    flush_rewrite_rules();

    wp_send_json_success(['success' => $result]);
}
add_action('wp_ajax_q_reinstall_service_worker', 'q_handle_reinstall_service_worker');

/**
 * Regenerate PWA service worker and manifest files
 */
function q_regenerate_pwa_files()
{
    // Update cache version to force refresh
    update_option('q_pwa_cache_version', '1.0.' . time());

    // Regenerate service workers
    q_copy_service_worker();

    // Regenerate PWA files if PWA is enabled
    if (get_option('q_pwa_enabled', false) && class_exists('Q_PWA')) {
        Q_PWA::create_physical_manifest_file();
        Q_PWA::create_physical_service_worker_file();

        // Create physical offline.html file
        q_create_physical_offline_file();
    }

    // Flush rewrite rules to ensure routes are registered
    flush_rewrite_rules();

    return true;
}

/**
 * Handle offline page template
 */
add_action('template_redirect', 'q_handle_offline_page');

function q_handle_offline_page()
{
    // Check if this is the offline page request
    if ((isset($_GET['q_offline']) || preg_match('#^/offline/?$#', $_SERVER['REQUEST_URI']))) {
        // Check if PWA is enabled
        $pwa_enabled = get_option('q_pwa_enabled', false);

        // Set proper headers
        header('Content-Type: text/html; charset=UTF-8');
        header('Cache-Control: public, max-age=86400');

        // Include the offline template
        if ($pwa_enabled && file_exists(Q_PLUGIN_DIR . 'includes/pwa/offline-template.php')) {
            include_once Q_PLUGIN_DIR . 'includes/pwa/offline-template.php';
            exit;
        } else {
            // Fallback to static offline.html if it exists
            $offline_html = ABSPATH . 'offline.html';
            if (file_exists($offline_html)) {
                readfile($offline_html);
                exit;
            } else {
                // Basic fallback if no template is available
                echo '<!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>You\'re Offline</title>
                    <style>
                        body { font-family: sans-serif; text-align: center; padding: 40px; }
                        h1 { color: #333; }
                    </style>
                </head>
                <body>
                    <h1>You\'re Offline</h1>
                    <p>Please check your internet connection and try again.</p>
                    <button onclick="window.location.reload()">Try Again</button>
                </body>
                </html>';
                exit;
            }
        }
    }
}

/**
 * Create physical offline.html file in WordPress root
 */
function q_create_physical_offline_file()
{
    $offline_html_path = ABSPATH . 'offline.html';
    $offline_template_path = Q_PLUGIN_DIR . 'includes/pwa/offline-template.php';

    // If the offline template exists, use it to generate the offline.html file
    if (file_exists($offline_template_path)) {
        // Start output buffering to capture the template output
        ob_start();
        include $offline_template_path;
        $offline_content = ob_get_clean();

        // Write the content to the offline.html file
        if (file_put_contents($offline_html_path, $offline_content) !== false) {
            error_log('Q-Pusher: Created physical offline.html file at ' . $offline_html_path);
            return true;
        } else {
            error_log('Q-Pusher: Failed to create physical offline.html file at ' . $offline_html_path);
            return false;
        }
    } else {
        // Use a basic offline page template if the template file doesn't exist
        $basic_offline_html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You\'re Offline</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background-color: #f1f1f1;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .offline-container {
            max-width: 600px;
            margin: 60px auto;
            padding: 40px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #666;
        }
        .retry-button {
            display: inline-block;
            background-color: #0384c6;
            color: #fff;
            padding: 12px 24px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .retry-button:hover {
            background-color: #026a9e;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <h1>You\'re Offline</h1>
        <p>It looks like you\'ve lost your internet connection. Please check your network settings and try again.</p>
        <p>Some content and features may be available offline if you\'ve visited them before.</p>
        <a href="javascript:window.location.reload()" class="retry-button">Try Again</a>
    </div>

    <script>
        // Check if we\'re back online
        window.addEventListener(\'online\', function() {
            window.location.reload();
        });
    </script>
</body>
</html>';

        if (file_put_contents($offline_html_path, $basic_offline_html) !== false) {
            error_log('Q-Pusher: Created basic physical offline.html file at ' . $offline_html_path);
            return true;
        } else {
            error_log('Q-Pusher: Failed to create basic physical offline.html file at ' . $offline_html_path);
            return false;
        }
    }
}

/**
 * Handle AJAX request to verify subscription status
 */
add_action('wp_ajax_q_verify_subscription', 'q_verify_subscription_callback');
add_action('wp_ajax_nopriv_q_verify_subscription', 'q_verify_subscription_callback');

function q_verify_subscription_callback()
{
    check_ajax_referer('q_subscribe_nonce', 'nonce');

    $token = sanitize_text_field($_POST['token']);
    $user_id = get_current_user_id();

    // Get stored token
    $stored_token = get_user_meta($user_id, 'q_push_token', true);

    wp_send_json_success([
        'isValid' => !empty($stored_token) && $stored_token === $token
    ]);
}

function q_validate_notification_payload($payload)
{
    $required_fields = ['title', 'message', 'token'];
    $errors = [];

    // Check required fields
    foreach ($required_fields as $field) {
        if (empty($payload[$field])) {
            $errors[] = "Missing required field: {$field}";
        }
    }

    // Validate token format
    if (!empty($payload['token']) && !preg_match('/^[a-zA-Z0-9:_-]{100,300}$/', $payload['token'])) {
        $errors[] = "Invalid token format";
    }

    // Validate title length
    if (strlen($payload['title']) > 100) {
        $errors[] = "Title exceeds maximum length of 100 characters";
    }

    // Validate message length
    if (strlen($payload['message']) > 2000) {
        $errors[] = "Message exceeds maximum length of 2000 characters";
    }

    // Validate image URL if present
    if (!empty($payload['image']) && !filter_var($payload['image'], FILTER_VALIDATE_URL)) {
        $errors[] = "Invalid image URL format";
    }

    return empty($errors) ? true : $errors;
}

function q_sanitize_input($data, $type = 'text')
{
    if (is_array($data)) {
        return array_map(function ($item) use ($type) {
            return q_sanitize_input($item, $type);
        }, $data);
    }

    $data = trim($data);

    switch ($type) {
        case 'email':
            return filter_var(sanitize_email($data), FILTER_VALIDATE_EMAIL) ? sanitize_email($data) : '';
        case 'url':
            return filter_var(esc_url_raw($data), FILTER_VALIDATE_URL) ? esc_url_raw($data) : '';
        case 'textarea':
            return wp_kses(sanitize_textarea_field($data), [
                'a' => ['href' => [], 'title' => []],
                'br' => [],
                'em' => [],
                'strong' => []
            ]);
        case 'key':
            return preg_replace('/[^a-zA-Z0-9_-]/', '', sanitize_key($data));
        case 'html':
            return wp_kses_post($data);
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT) !== false ? intval($data) : 0;
        case 'float':
            return filter_var($data, FILTER_VALIDATE_FLOAT) !== false ? floatval($data) : 0.0;
        case 'boolean':
            return filter_var($data, FILTER_VALIDATE_BOOLEAN);
        default:
            return sanitize_text_field($data);
    }
}

/**
 * Get the number of devices registered for a user
 *
 * @param int $user_id The user ID
 * @return int Number of registered devices
 */
function q_get_user_device_count($user_id)
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_devices';

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
        $user_id
    ));

    return (int) $count;
}
