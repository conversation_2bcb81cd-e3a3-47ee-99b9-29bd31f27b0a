<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/policy.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Provides the configuration for logging a type of permissions.
 * Example:
 *     {
 *       "audit_log_configs": [
 *         {
 *           "log_type": "DATA_READ",
 *           "exempted_members": [
 *             "user:jose&#64;example.com"
 *           ]
 *         },
 *         {
 *           "log_type": "DATA_WRITE"
 *         }
 *       ]
 *     }
 * This enables 'DATA_READ' and 'DATA_WRITE' logging, while exempting
 * jose&#64;example.com from DATA_READ logging.
 *
 * Generated from protobuf message <code>google.iam.v1.AuditLogConfig</code>
 */
class AuditLogConfig extends \Google\Protobuf\Internal\Message
{
    /**
     * The log type that this config enables.
     *
     * Generated from protobuf field <code>.google.iam.v1.AuditLogConfig.LogType log_type = 1;</code>
     */
    protected $log_type = 0;
    /**
     * Specifies the identities that do not cause logging for this type of
     * permission.
     * Follows the same format of
     * [Binding.members][google.iam.v1.Binding.members].
     *
     * Generated from protobuf field <code>repeated string exempted_members = 2;</code>
     */
    private $exempted_members;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $log_type
     *           The log type that this config enables.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $exempted_members
     *           Specifies the identities that do not cause logging for this type of
     *           permission.
     *           Follows the same format of
     *           [Binding.members][google.iam.v1.Binding.members].
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\Policy::initOnce();
        parent::__construct($data);
    }

    /**
     * The log type that this config enables.
     *
     * Generated from protobuf field <code>.google.iam.v1.AuditLogConfig.LogType log_type = 1;</code>
     * @return int
     */
    public function getLogType()
    {
        return $this->log_type;
    }

    /**
     * The log type that this config enables.
     *
     * Generated from protobuf field <code>.google.iam.v1.AuditLogConfig.LogType log_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setLogType($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\Iam\V1\AuditLogConfig\LogType::class);
        $this->log_type = $var;

        return $this;
    }

    /**
     * Specifies the identities that do not cause logging for this type of
     * permission.
     * Follows the same format of
     * [Binding.members][google.iam.v1.Binding.members].
     *
     * Generated from protobuf field <code>repeated string exempted_members = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExemptedMembers()
    {
        return $this->exempted_members;
    }

    /**
     * Specifies the identities that do not cause logging for this type of
     * permission.
     * Follows the same format of
     * [Binding.members][google.iam.v1.Binding.members].
     *
     * Generated from protobuf field <code>repeated string exempted_members = 2;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExemptedMembers($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->exempted_members = $arr;

        return $this;
    }

}

