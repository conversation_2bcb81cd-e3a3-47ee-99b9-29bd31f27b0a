<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Settings for Dotnet client libraries.
 *
 * Generated from protobuf message <code>google.api.DotnetSettings</code>
 */
class DotnetSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     */
    protected $common = null;
    /**
     * Map from original service names to renamed versions.
     * This is used when the default generated types
     * would cause a naming conflict. (Neither name is
     * fully-qualified.)
     * Example: Subscriber to SubscriberServiceApi.
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     */
    private $renamed_services;
    /**
     * Map from full resource types to the effective short name
     * for the resource. This is used when otherwise resource
     * named from different services would cause naming collisions.
     * Example entry:
     * "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
     *
     * Generated from protobuf field <code>map<string, string> renamed_resources = 3;</code>
     */
    private $renamed_resources;
    /**
     * List of full resource types to ignore during generation.
     * This is typically used for API-specific Location resources,
     * which should be handled by the generator as if they were actually
     * the common Location resources.
     * Example entry: "documentai.googleapis.com/Location"
     *
     * Generated from protobuf field <code>repeated string ignored_resources = 4;</code>
     */
    private $ignored_resources;
    /**
     * Namespaces which must be aliased in snippets due to
     * a known (but non-generator-predictable) naming collision
     *
     * Generated from protobuf field <code>repeated string forced_namespace_aliases = 5;</code>
     */
    private $forced_namespace_aliases;
    /**
     * Method signatures (in the form "service.method(signature)")
     * which are provided separately, so shouldn't be generated.
     * Snippets *calling* these methods are still generated, however.
     *
     * Generated from protobuf field <code>repeated string handwritten_signatures = 6;</code>
     */
    private $handwritten_signatures;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\CommonLanguageSettings $common
     *           Some settings.
     *     @type array|\Google\Protobuf\Internal\MapField $renamed_services
     *           Map from original service names to renamed versions.
     *           This is used when the default generated types
     *           would cause a naming conflict. (Neither name is
     *           fully-qualified.)
     *           Example: Subscriber to SubscriberServiceApi.
     *     @type array|\Google\Protobuf\Internal\MapField $renamed_resources
     *           Map from full resource types to the effective short name
     *           for the resource. This is used when otherwise resource
     *           named from different services would cause naming collisions.
     *           Example entry:
     *           "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $ignored_resources
     *           List of full resource types to ignore during generation.
     *           This is typically used for API-specific Location resources,
     *           which should be handled by the generator as if they were actually
     *           the common Location resources.
     *           Example entry: "documentai.googleapis.com/Location"
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $forced_namespace_aliases
     *           Namespaces which must be aliased in snippets due to
     *           a known (but non-generator-predictable) naming collision
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $handwritten_signatures
     *           Method signatures (in the form "service.method(signature)")
     *           which are provided separately, so shouldn't be generated.
     *           Snippets *calling* these methods are still generated, however.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     * @return \Google\Api\CommonLanguageSettings|null
     */
    public function getCommon()
    {
        return $this->common;
    }

    public function hasCommon()
    {
        return isset($this->common);
    }

    public function clearCommon()
    {
        unset($this->common);
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     * @param \Google\Api\CommonLanguageSettings $var
     * @return $this
     */
    public function setCommon($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\CommonLanguageSettings::class);
        $this->common = $var;

        return $this;
    }

    /**
     * Map from original service names to renamed versions.
     * This is used when the default generated types
     * would cause a naming conflict. (Neither name is
     * fully-qualified.)
     * Example: Subscriber to SubscriberServiceApi.
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getRenamedServices()
    {
        return $this->renamed_services;
    }

    /**
     * Map from original service names to renamed versions.
     * This is used when the default generated types
     * would cause a naming conflict. (Neither name is
     * fully-qualified.)
     * Example: Subscriber to SubscriberServiceApi.
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setRenamedServices($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->renamed_services = $arr;

        return $this;
    }

    /**
     * Map from full resource types to the effective short name
     * for the resource. This is used when otherwise resource
     * named from different services would cause naming collisions.
     * Example entry:
     * "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
     *
     * Generated from protobuf field <code>map<string, string> renamed_resources = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getRenamedResources()
    {
        return $this->renamed_resources;
    }

    /**
     * Map from full resource types to the effective short name
     * for the resource. This is used when otherwise resource
     * named from different services would cause naming collisions.
     * Example entry:
     * "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
     *
     * Generated from protobuf field <code>map<string, string> renamed_resources = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setRenamedResources($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->renamed_resources = $arr;

        return $this;
    }

    /**
     * List of full resource types to ignore during generation.
     * This is typically used for API-specific Location resources,
     * which should be handled by the generator as if they were actually
     * the common Location resources.
     * Example entry: "documentai.googleapis.com/Location"
     *
     * Generated from protobuf field <code>repeated string ignored_resources = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getIgnoredResources()
    {
        return $this->ignored_resources;
    }

    /**
     * List of full resource types to ignore during generation.
     * This is typically used for API-specific Location resources,
     * which should be handled by the generator as if they were actually
     * the common Location resources.
     * Example entry: "documentai.googleapis.com/Location"
     *
     * Generated from protobuf field <code>repeated string ignored_resources = 4;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setIgnoredResources($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->ignored_resources = $arr;

        return $this;
    }

    /**
     * Namespaces which must be aliased in snippets due to
     * a known (but non-generator-predictable) naming collision
     *
     * Generated from protobuf field <code>repeated string forced_namespace_aliases = 5;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getForcedNamespaceAliases()
    {
        return $this->forced_namespace_aliases;
    }

    /**
     * Namespaces which must be aliased in snippets due to
     * a known (but non-generator-predictable) naming collision
     *
     * Generated from protobuf field <code>repeated string forced_namespace_aliases = 5;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setForcedNamespaceAliases($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->forced_namespace_aliases = $arr;

        return $this;
    }

    /**
     * Method signatures (in the form "service.method(signature)")
     * which are provided separately, so shouldn't be generated.
     * Snippets *calling* these methods are still generated, however.
     *
     * Generated from protobuf field <code>repeated string handwritten_signatures = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getHandwrittenSignatures()
    {
        return $this->handwritten_signatures;
    }

    /**
     * Method signatures (in the form "service.method(signature)")
     * which are provided separately, so shouldn't be generated.
     * Snippets *calling* these methods are still generated, however.
     *
     * Generated from protobuf field <code>repeated string handwritten_signatures = 6;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setHandwrittenSignatures($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->handwritten_signatures = $arr;

        return $this;
    }

}

