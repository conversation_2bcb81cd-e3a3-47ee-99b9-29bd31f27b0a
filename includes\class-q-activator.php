<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_Activator {
    public static function activate() {
        global $wpdb;
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $charset_collate = $wpdb->get_charset_collate();

        // Notifications table
        $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}q_notifications (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            message text NOT NULL,
            icon varchar(255),
            click_action varchar(255),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) $charset_collate;";
        dbDelta($sql);

        // Notification logs table
        $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}q_notification_logs (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            notification_id bigint(20) NOT NULL,
            device_id varchar(255) NOT NULL,
            status varchar(50) NOT NULL,
            sent_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY notification_id (notification_id),
            KEY device_id (device_id)
        ) $charset_collate;";
        dbDelta($sql);

        // Devices table
        $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}q_devices (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            token varchar(255) NOT NULL,
            user_id bigint(20),
            platform varchar(50),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            last_active datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY token (token),
            KEY user_id (user_id)
        ) $charset_collate;";
        dbDelta($sql);

        update_option('q_db_version', Q_DB_VERSION);
    }
}

