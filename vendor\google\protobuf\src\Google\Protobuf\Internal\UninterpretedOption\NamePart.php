<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\UninterpretedOption;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\GPBWire;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\InputStream;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The name of the uninterpreted option.  Each string represents a segment in
 * a dot-separated name.  is_extension is true iff a segment represents an
 * extension (denoted with parentheses in options specs in .proto files).
 * E.g.,{ ["foo", false], ["bar.baz", true], ["moo", false] } represents
 * "foo.(bar.baz).moo".
 *
 * Generated from protobuf message <code>google.protobuf.UninterpretedOption.NamePart</code>
 */
class NamePart extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>required string name_part = 1;</code>
     */
    protected $name_part = null;
    /**
     * Generated from protobuf field <code>required bool is_extension = 2;</code>
     */
    protected $is_extension = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $name_part
     *     @type bool $is_extension
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\Internal\Descriptor::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>required string name_part = 1;</code>
     * @return string
     */
    public function getNamePart()
    {
        return isset($this->name_part) ? $this->name_part : '';
    }

    public function hasNamePart()
    {
        return isset($this->name_part);
    }

    public function clearNamePart()
    {
        unset($this->name_part);
    }

    /**
     * Generated from protobuf field <code>required string name_part = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setNamePart($var)
    {
        GPBUtil::checkString($var, True);
        $this->name_part = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>required bool is_extension = 2;</code>
     * @return bool
     */
    public function getIsExtension()
    {
        return isset($this->is_extension) ? $this->is_extension : false;
    }

    public function hasIsExtension()
    {
        return isset($this->is_extension);
    }

    public function clearIsExtension()
    {
        unset($this->is_extension);
    }

    /**
     * Generated from protobuf field <code>required bool is_extension = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsExtension($var)
    {
        GPBUtil::checkBool($var);
        $this->is_extension = $var;

        return $this;
    }

}

