// Q-<PERSON>usher PWA Service Worker
const CACHE_NAME = '{{CACHE_NAME}}-{{CACHE_VERSION}}';
const OFFLINE_PAGE = '{{OFFLINE_PAGE}}';
const CACHE_URLS = {{CACHE_URLS}};

// Cache strategies
const CACHE_STRATEGIES = {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
    NETWORK_ONLY: 'network-only',
    CACHE_ONLY: 'cache-only'
};

// Background sync queue name
const SYNC_QUEUE_NAME = 'q-pwa-sync-queue';

// IndexedDB for background sync
let syncDB;

// Initialize IndexedDB for background sync
async function initSyncDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('q-pwa-sync', 1);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);

        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('sync-queue')) {
                const store = db.createObjectStore('sync-queue', { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };
    });
}

// Install event - cache assets and initialize sync
self.addEventListener('install', event => {
    console.log('[Q-PWA] Service Worker installing');

    event.waitUntil(
        Promise.all([
            initSyncDB().then(db => { syncDB = db; }),
            caches.open(CACHE_NAME).then(cache => {
                console.log('[Q-PWA] Caching pages');

                // Ensure CACHE_URLS is an array
                let urlsToCache = CACHE_URLS;
                if (typeof CACHE_URLS === 'string') {
                    try {
                        urlsToCache = JSON.parse(CACHE_URLS);
                    } catch (e) {
                        console.error('[Q-PWA] Failed to parse CACHE_URLS:', e);
                        urlsToCache = ['/'];
                    }
                }

                if (!Array.isArray(urlsToCache)) {
                    console.error('[Q-PWA] CACHE_URLS is not an array, using default');
                    urlsToCache = ['/'];
                }

                // Make sure offline page is in the cache list
                const offlinePageUrl = new URL(OFFLINE_PAGE, self.location.origin).pathname;
                if (!urlsToCache.includes(offlinePageUrl)) {
                    urlsToCache.push(offlinePageUrl);
                }

                // Also add fallback offline paths for compatibility
                if (!urlsToCache.includes('/offline.html')) {
                    urlsToCache.push('/offline.html');
                }
                if (!urlsToCache.includes('/offline/index.html')) {
                    urlsToCache.push('/offline/index.html');
                }

                console.log('[Q-PWA] URLs to cache:', urlsToCache);

                // Cache each URL individually to avoid issues with addAll
                const cachePromises = urlsToCache.map(url => {
                    // Skip the /offline/ URL if it's in the list (we'll use offline.html instead)
                    if (url === '/offline/') {
                        console.log(`[Q-PWA] Skipping /offline/ URL, using /offline.html instead`);
                        return Promise.resolve();
                    }

                    // Create a proper request object with credentials
                    const request = new Request(url, { credentials: 'same-origin' });

                    return fetch(request)
                        .then(response => {
                            if (!response.ok) {
                                // For offline.html and offline/index.html, create a fallback if they don't exist
                                if (url === '/offline.html' || url === '/offline/index.html') {
                                    console.log(`[Q-PWA] Creating fallback for ${url}`);
                                    const fallbackResponse = new Response(
                                        '<html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
                                        {
                                            status: 200,
                                            headers: { 'Content-Type': 'text/html' }
                                        }
                                    );
                                    return cache.put(request, fallbackResponse);
                                }
                                throw new Error(`Request for ${url} returned status ${response.status}`);
                            }
                            return cache.put(request, response);
                        })
                        .catch(error => {
                            console.error(`[Q-PWA] Failed to cache ${url}:`, error);
                        });
                });

                return Promise.all(cachePromises);
            })
        ])
        .then(() => {
            console.log('[Q-PWA] Installed successfully');
            return self.skipWaiting();
        })
        .catch(error => {
            console.error('[Q-PWA] Cache install error:', error);
        })
    );
});

// Activate event - clean up old caches and initialize sync DB
self.addEventListener('activate', event => {
    console.log('[Q-PWA] Service Worker activating');

    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames
                        .filter(cacheName => {
                            return cacheName.startsWith('{{CACHE_NAME}}-') && cacheName !== CACHE_NAME;
                        })
                        .map(cacheName => {
                            console.log('[Q-PWA] Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        })
                );
            }),
            // Initialize sync DB if not already done
            !syncDB ? initSyncDB().then(db => { syncDB = db; }) : Promise.resolve()
        ])
        .then(() => {
            console.log('[Q-PWA] Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Background sync event
self.addEventListener('sync', event => {
    console.log('[Q-PWA] Background sync triggered:', event.tag);

    if (event.tag === SYNC_QUEUE_NAME) {
        event.waitUntil(processSyncQueue());
    }
});

// Process background sync queue
async function processSyncQueue() {
    if (!syncDB) {
        console.error('[Q-PWA] Sync DB not initialized');
        return;
    }

    try {
        const transaction = syncDB.transaction(['sync-queue'], 'readwrite');
        const store = transaction.objectStore('sync-queue');
        const request = store.getAll();

        request.onsuccess = async () => {
            const items = request.result;
            console.log(`[Q-PWA] Processing ${items.length} sync items`);

            for (const item of items) {
                try {
                    await processQueueItem(item);
                    // Remove successfully processed item
                    store.delete(item.id);
                } catch (error) {
                    console.error('[Q-PWA] Failed to process sync item:', error);
                    // Keep item in queue for retry
                }
            }
        };
    } catch (error) {
        console.error('[Q-PWA] Error processing sync queue:', error);
    }
}

// Process individual queue item
async function processQueueItem(item) {
    const { url, method, headers, body } = item.data;

    const response = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
    });

    if (!response.ok) {
        throw new Error(`Sync request failed: ${response.status}`);
    }

    console.log('[Q-PWA] Sync item processed successfully:', item.id);
    return response;
}

// Add item to sync queue
async function addToSyncQueue(data) {
    if (!syncDB) {
        console.error('[Q-PWA] Sync DB not initialized');
        return;
    }

    const transaction = syncDB.transaction(['sync-queue'], 'readwrite');
    const store = transaction.objectStore('sync-queue');

    const item = {
        data,
        timestamp: Date.now()
    };

    store.add(item);

    // Register for background sync
    if ('serviceWorker' in self && 'sync' in self.registration) {
        await self.registration.sync.register(SYNC_QUEUE_NAME);
    }
}

// Determine caching strategy based on request
function getCacheStrategy(request) {
    const url = new URL(request.url);

    // API requests - Network first
    if (url.pathname.includes('/wp-json/') || url.pathname.includes('/wp-admin/admin-ajax.php')) {
        return CACHE_STRATEGIES.NETWORK_FIRST;
    }

    // Static assets - Cache first
    if (url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/)) {
        return CACHE_STRATEGIES.CACHE_FIRST;
    }

    // HTML pages - Stale while revalidate
    if (request.headers.get('Accept')?.includes('text/html')) {
        return CACHE_STRATEGIES.STALE_WHILE_REVALIDATE;
    }

    // Default - Network first
    return CACHE_STRATEGIES.NETWORK_FIRST;
}

// Cache first strategy
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }

    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('[Q-PWA] Cache first failed:', error);
        throw error;
    }
}

// Network first strategy
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('[Q-PWA] Network failed, trying cache:', error.message);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);

    const networkResponsePromise = fetch(request).then(response => {
        if (response.ok) {
            const cache = caches.open(CACHE_NAME);
            cache.then(c => c.put(request, response.clone()));
        }
        return response;
    }).catch(error => {
        console.log('[Q-PWA] Network update failed:', error.message);
        return null;
    });

    return cachedResponse || networkResponsePromise;
}

// Enhanced fetch event handler
self.addEventListener('fetch', event => {
    const request = event.request;

    // Skip cross-origin requests
    if (!request.url.startsWith(self.location.origin)) {
        return;
    }

    // Handle non-GET requests differently
    if (request.method !== 'GET') {
        // For POST requests, try network first, queue for background sync if offline
        if (request.method === 'POST') {
            event.respondWith(
                fetch(request).catch(async () => {
                    // Queue for background sync
                    await addToSyncQueue({
                        url: request.url,
                        method: request.method,
                        headers: Object.fromEntries(request.headers.entries()),
                        body: await request.text()
                    });

                    return new Response(JSON.stringify({
                        success: false,
                        message: 'Request queued for background sync'
                    }), {
                        status: 202,
                        headers: { 'Content-Type': 'application/json' }
                    });
                })
            );
        }
        return;
    }

    // Skip admin and login pages
    if (request.url.includes('/wp-admin/') ||
        request.url.includes('/wp-login.php') ||
        request.url.includes('preview=true')) {
        return;
    }

    // Skip Firebase messaging requests
    if (request.url.includes('firebase-messaging-sw.js')) {
        return;
    }

    // Apply caching strategy
    const strategy = getCacheStrategy(request);

    event.respondWith(
        (async () => {
            try {
                switch (strategy) {
                    case CACHE_STRATEGIES.CACHE_FIRST:
                        return await cacheFirst(request);
                    case CACHE_STRATEGIES.NETWORK_FIRST:
                        return await networkFirst(request);
                    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
                        return await staleWhileRevalidate(request);
                    default:
                        return await networkFirst(request);
                }
            } catch (error) {
                console.error('[Q-PWA] Fetch strategy failed:', error);

                // Fallback to offline page for HTML requests
                if (request.headers.get('Accept')?.includes('text/html')) {
                    const offlineResponse = await caches.match(OFFLINE_PAGE) ||
                                          await caches.match('/offline.html') ||
                                          await caches.match('/offline/index.html');

                    if (offlineResponse) {
                        return offlineResponse;
                    }

                    // Create basic offline response
                    return new Response(
                        '<html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
                        {
                            status: 200,
                            headers: { 'Content-Type': 'text/html' }
                        }
                    );
                }

                // For other requests, return network error
                return new Response('Network error', {
                    status: 408,
                    headers: { 'Content-Type': 'text/plain' }
                });
            }
        })()
    );
});

// Handle messages from the main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Import Firebase service worker functionality
// This section ensures compatibility with the Firebase messaging service worker
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging-compat.js');

// Initialize Firebase with configuration details and error handling
try {
    firebase.initializeApp({
        apiKey: "{{FIREBASE_API_KEY}}",
        authDomain: "{{FIREBASE_AUTH_DOMAIN}}",
        projectId: "{{FIREBASE_PROJECT_ID}}",
        storageBucket: "{{FIREBASE_STORAGE_BUCKET}}",
        messagingSenderId: "{{FIREBASE_MESSAGING_SENDER_ID}}",
        appId: "{{FIREBASE_APP_ID}}"
    });
    const messaging = firebase.messaging();

    // Enhanced notification tracking with better validation
    const notificationCache = {
        messages: new Map(),
        cleanupInterval: 60000, // 60 seconds
        maxAge: 60000, // 60 seconds

        addMessage(key, timestamp) {
            if (!key) return false;
            this.cleanup();
            this.messages.set(key, timestamp);
            return true;
        },

        hasMessage(key) {
            if (!key) return false;
            this.cleanup();
            return this.messages.has(key);
        },

        cleanup() {
            const now = Date.now();
            let cleaned = 0;
            for (const [key, timestamp] of this.messages.entries()) {
                if (now - timestamp > this.maxAge) {
                    this.messages.delete(key);
                    cleaned++;
                }
            }
            if (cleaned > 0) {
                console.log(`Cleaned ${cleaned} old notifications`);
            }
        }
    };

    // Handle background messages with improved error handling and prevent duplicate notifications
    messaging.onBackgroundMessage(async (payload) => {
        try {
            console.log('Processing background message:', payload);

            // Handle data-only messages
            const { data } = payload;
            if (!data?.title) {
                console.error('Invalid notification payload');
                return;
            }

            const timestamp = parseInt(data.timestamp || Date.now() / 1000) * 1000;
            const now = Date.now();
            const notificationKey = data.message_id || `${data.title}-${timestamp}`;

            if (notificationCache.hasMessage(notificationKey)) {
                console.log('Duplicate notification filtered:', notificationKey);
                return;
            }

            if (now - timestamp > notificationCache.maxAge) {
                console.log('Discarding outdated notification:', notificationKey);
                return;
            }

            notificationCache.addMessage(notificationKey, now);

            // Create and show the notification
            const notificationOptions = {
                title: data.title,
                body: data.body,
                icon: data.icon || '/favicon.ico',
                data: {
                    url: data.click_action || self.registration.scope,
                    notification_id: data.notification_id || '',
                    form_id: data.form_id || ''
                },
                requireInteraction: true,
                tag: data.message_id || `notification-${Date.now()}`,
                click_action: data.click_action || self.registration.scope,
                badge: data.badge || data.icon || '/favicon.ico'
            };

            if (data.image) {
                notificationOptions.image = data.image;
            }

            return self.registration.showNotification(data.title, notificationOptions);
        } catch (error) {
            console.error('Error showing notification:', error);
        }
    });

    // Notification click handler with improved navigation
    self.addEventListener('notificationclick', event => {
        try {
            event.notification.close();

            // Get URL from notification data or fallback to scope
            let navigateUrl = event.notification.data?.url || self.registration.scope;

            // Ensure URL is absolute
            if (!navigateUrl.startsWith('http')) {
                navigateUrl = self.registration.scope + navigateUrl.replace(/^\//, '');
            }

            // Notify client to clear badge count
            self.clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({
                        type: 'NOTIFICATION_CLICKED',
                        notification: {
                            id: event.notification.data?.notification_id || '',
                            title: event.notification.title
                        }
                    });
                });
            });

            // Track notification click with form_id
            const notificationData = event.notification.data || {};
            if (notificationData.notification_id) {
                fetch('/wp-admin/admin-ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'q_track_notification_click',
                        notification_id: notificationData.notification_id,
                        form_id: notificationData.form_id || ''
                    })
                }).catch(console.error);
            }

            // Focus existing window or open new one
            event.waitUntil(
                clients.matchAll({
                    type: 'window',
                    includeUncontrolled: true
                })
                .then(clientList => {
                    // Try to find an existing tab with the same origin
                    const matchingClient = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (matchingClient) {
                        // If we found a matching tab, focus it and navigate
                        return matchingClient.focus().then(client => {
                            // Only navigate if URLs are different
                            if (client.url !== navigateUrl) {
                                return client.navigate(navigateUrl);
                            }
                        });
                    }

                    // If no existing tab found, check if we have any tab from our origin
                    const anyClientFromOrigin = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (anyClientFromOrigin) {
                        // Reuse existing tab from same origin
                        return anyClientFromOrigin.focus().then(client => {
                            return client.navigate(navigateUrl);
                        });
                    }

                    // If no existing tabs found, open a new one
                    return clients.openWindow(navigateUrl);
                })
            );
        } catch (error) {
            console.error('Error handling notification click:', error);
            // Fallback to simple window open
            event.waitUntil(clients.openWindow(self.registration.scope));
        }
    });

    // Automated cleanup
    setInterval(() => notificationCache.cleanup(), notificationCache.cleanupInterval);

} catch (error) {
    console.error('Firebase initialization failed:', error);
}
