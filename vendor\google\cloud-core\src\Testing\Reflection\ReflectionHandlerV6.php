<?php
/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Cloud\Core\Testing\Reflection;

use PhpParser\ParserFactory;
use PhpParser\PhpVersion;

/**
 * Class for running snippets using phpdocumentor/reflection:v6.
 *
 * @internal
 */
class ReflectionHandlerV6 extends ReflectionHandlerV5
{
    /**
     * @see ReflectionHandlerV5
     */
    protected function createParser()
    {
        $phpVersion = PhpVersion::fromString('8.1');  // PHP 8.1.0
        return (new ParserFactory())->createForVersion($phpVersion);
    }

    /**
     * @see ReflectionHandlerV5
     */
    protected function getAdditionalStrategies()
    {
        return [];
    }
}
