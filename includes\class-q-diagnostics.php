<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Diagnostics
{
    public static function run_diagnostics()
    {
        $results = [];

        // Check PHP version
        $results['php_version'] = [
            'test' => 'PHP Version Check',
            'result' => version_compare(PHP_VERSION, '8.1.0', '>='),
            'value' => PHP_VERSION,
            'required' => '8.1.0 or higher'
        ];

        // Check Firebase configuration
        $firebase_config = get_option('q_firebase_config');
        $config_type = 'Missing';

        if (!empty($firebase_config)) {
            $decoded = json_decode($firebase_config, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded) && !empty($decoded['projectId'])) {
                $config_type = 'JSON Configuration';
            } else {
                $config_type = 'Configured (Invalid JSON)';
            }
        }

        $results['firebase_config'] = [
            'test' => 'Firebase Configuration',
            'result' => !empty($firebase_config),
            'value' => $config_type
        ];

        // Check for Firebase config in JSON format first
        $json_config = get_option('q_firebase_config');
        $json_values = [];

        if (!empty($json_config)) {
            $decoded = json_decode($json_config, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $json_values = $decoded;
            }
        }

        // Define the config keys to check
        $config_keys = [
            'apiKey' => 'q_firebase_api_key',
            'authDomain' => 'q_firebase_auth_domain',
            'projectId' => 'q_firebase_project_id',
            'storageBucket' => 'q_firebase_storage_bucket',
            'messagingSenderId' => 'q_firebase_messaging_sender_id',
            'appId' => 'q_firebase_app_id'
        ];

        foreach ($config_keys as $key => $option_name) {
            // Check if value exists in JSON config
            $json_value = isset($json_values[$key]) ? $json_values[$key] : '';

            // Check if value exists in individual option
            $individual_value = get_option($option_name);

            // Value is configured if it exists in either place
            $is_configured = !empty($json_value) || !empty($individual_value);
            $value_to_show = !empty($json_value) ? 'Configured in JSON' : (!empty($individual_value) ? 'Configured' : 'Missing');

            $results['firebase_' . $key] = [
                'test' => "Firebase $key",
                'result' => $is_configured,
                'value' => $value_to_show
            ];
        }

        // Check service worker file
        $sw_path = ABSPATH . 'firebase-messaging-sw.js';
        $results['service_worker'] = [
            'test' => 'Firebase Service Worker',
            'result' => file_exists($sw_path),
            'value' => file_exists($sw_path) ? 'Installed' : 'Missing'
        ];

        // Check if service worker has placeholders
        if (file_exists($sw_path)) {
            $sw_content = file_get_contents($sw_path);
            $has_placeholders = strpos($sw_content, 'FIREBASE_') !== false;
            $results['service_worker_config'] = [
                'test' => 'Service Worker Configuration',
                'result' => !$has_placeholders,
                'value' => $has_placeholders ? 'Contains placeholders' : 'Properly configured'
            ];
        }

        // Check PWA files if PWA is enabled
        if (get_option('q_pwa_enabled', false)) {
            // Check PWA service worker
            $pwa_sw_path = ABSPATH . 'q-sw.js';
            $results['pwa_service_worker'] = [
                'test' => 'PWA Service Worker',
                'result' => file_exists($pwa_sw_path),
                'value' => file_exists($pwa_sw_path) ? 'Installed' : 'Missing'
            ];

            // Check PWA manifest
            $manifest_path = ABSPATH . 'q-manifest.json';
            $results['pwa_manifest'] = [
                'test' => 'PWA Manifest',
                'result' => file_exists($manifest_path),
                'value' => file_exists($manifest_path) ? 'Installed' : 'Missing'
            ];

            // Check offline page accessibility
            $offline_url = home_url('/offline/');
            $response = wp_remote_get($offline_url);
            $response_code = wp_remote_retrieve_response_code($response);
            $results['offline_page'] = [
                'test' => 'Offline Page',
                'result' => $response_code === 200,
                'value' => $response_code === 200 ? 'Accessible' : 'Not accessible (HTTP ' . $response_code . ')'
            ];
        }

        // Check database tables
        global $wpdb;
        $tables = [
            $wpdb->prefix . 'q_notifications',
            $wpdb->prefix . 'q_notification_logs',
            $wpdb->prefix . 'q_devices'
        ];

        foreach ($tables as $table) {
            $results['table_' . $table] = [
                'test' => "Database Table: $table",
                'result' => $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table,
                'value' => $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table ? 'Exists' : 'Missing'
            ];
        }

        // Check write permissions
        $upload_dir = wp_upload_dir();
        $results['write_permissions'] = [
            'test' => 'Write Permissions',
            'result' => is_writable($upload_dir['basedir']),
            'value' => is_writable($upload_dir['basedir']) ? 'Writable' : 'Not writable'
        ];

        // Check WordPress root directory write permissions
        $results['root_write_permissions'] = [
            'test' => 'WordPress Root Write Permissions',
            'result' => is_writable(ABSPATH),
            'value' => is_writable(ABSPATH) ? 'Writable' : 'Not writable'
        ];

        return $results;
    }

    public static function display_diagnostics_page()
    {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Handle service worker reinstall
        if (isset($_POST['reinstall_service_worker']) && check_admin_referer('q_reinstall_service_worker')) {
            $result = q_copy_service_worker();
            if ($result) {
                echo '<div class="notice notice-success"><p>Firebase service worker reinstalled successfully!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Failed to reinstall Firebase service worker. Check error logs for details.</p></div>';
            }
        }

        // Handle PWA files reinstall
        if (isset($_POST['reinstall_pwa_files']) && check_admin_referer('q_reinstall_pwa_files')) {
            $pwa_enabled = get_option('q_pwa_enabled', false);

            if (!$pwa_enabled) {
                echo '<div class="notice notice-warning"><p>PWA functionality is not enabled. Please enable it in the Q-Pusher settings first.</p></div>';
            } else if (!class_exists('Q_PWA')) {
                echo '<div class="notice notice-error"><p>PWA class not found. Please check your installation.</p></div>';
            } else {
                // Use the regenerate function to ensure everything is updated
                $result = q_regenerate_pwa_files();

                if ($result) {
                    echo '<div class="notice notice-success"><p>PWA files reinstalled successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to reinstall some PWA files. Check error logs for details.</p></div>';
                }
            }
        }

        // Handle offline page test
        if (isset($_POST['test_offline_page']) && check_admin_referer('q_test_offline_page')) {
            $offline_url = home_url('/offline/');
            $response = wp_remote_get($offline_url);
            $response_code = wp_remote_retrieve_response_code($response);

            if ($response_code === 200) {
                echo '<div class="notice notice-success"><p>Offline page is accessible at ' . esc_url($offline_url) . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Offline page is not accessible. Received HTTP ' . esc_html($response_code) . ' response. Please check your rewrite rules and server configuration.</p></div>';
            }
        }

        $results = self::run_diagnostics();

        // Check if we're using JSON configuration
        $using_json = false;
        $json_config = get_option('q_firebase_config');
        if (!empty($json_config)) {
            $decoded = json_decode($json_config, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded) && !empty($decoded['projectId'])) {
                $using_json = true;
            }
        }
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <?php if ($using_json): ?>
                <div class="notice notice-info" style="margin: 10px 0;">
                    <p><strong>Note:</strong> Your Firebase configuration is stored in JSON format. Individual configuration fields
                        may show as "Missing" but this is normal when using the JSON configuration method.</p>
                </div>
            <?php endif; ?>

            <h2>Diagnostics Results</h2>
            <table class="widefat">
                <thead>
                    <tr>
                        <th>Test</th>
                        <th>Status</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($results as $test): ?>
                        <tr>
                            <td><?php echo esc_html($test['test']); ?></td>
                            <td><?php echo $test['result'] ? '✅' : '❌'; ?></td>
                            <td><?php echo esc_html($test['value']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <h2>Service Worker Management</h2>
            <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
                <h3>Reinstall Firebase Service Worker</h3>
                <p>If you're experiencing issues with push notifications, try reinstalling the Firebase service worker:</p>
                <form method="post">
                    <?php wp_nonce_field('q_reinstall_service_worker'); ?>
                    <input type="submit" name="reinstall_service_worker" class="button button-primary"
                        value="Reinstall Firebase Service Worker">
                </form>
            </div>

            <?php if (get_option('q_pwa_enabled', false)): ?>
                <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
                    <h3>Reinstall PWA Files</h3>
                    <p>If you're experiencing issues with PWA functionality, try reinstalling the PWA files:</p>
                    <form method="post">
                        <?php wp_nonce_field('q_reinstall_pwa_files'); ?>
                        <input type="submit" name="reinstall_pwa_files" class="button button-primary" value="Reinstall PWA Files">
                    </form>
                </div>

                <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
                    <h3>Test Offline Page</h3>
                    <p>Test if the offline page is accessible. This is required for the PWA to work properly when offline:</p>
                    <form method="post">
                        <?php wp_nonce_field('q_test_offline_page'); ?>
                        <input type="submit" name="test_offline_page" class="button button-primary" value="Test Offline Page">
                    </form>
                    <p style="margin-top: 10px;"><small>The offline page should be accessible at:
                            <code><?php echo esc_url(home_url('/offline/')); ?></code></small></p>
                </div>
            <?php endif; ?>

            <h2>Firebase Configuration</h2>
            <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
                <h3>Configuration Methods</h3>
                <p>Q-Pusher supports two methods of Firebase configuration:</p>
                <ol>
                    <li><strong>JSON Configuration (Recommended)</strong>: Paste your entire Firebase configuration as JSON in
                        the plugin settings.</li>
                    <li><strong>Individual Fields</strong>: Configure each Firebase setting individually.</li>
                </ol>
                <p>If you're using the JSON method (which is recommended), the individual field tests may show as "Missing" in
                    the diagnostics, but this is normal and won't affect functionality.</p>

                <p>To update your Firebase configuration, go to <a
                        href="<?php echo admin_url('options-general.php?page=q-pusher-settings'); ?>">Q Pusher Settings</a>.</p>
            </div>
        </div>

        <script>
            jQuery(document).ready(function ($) {
                // Add AJAX functionality if needed
            });
        </script>
        <?php
    }
}
