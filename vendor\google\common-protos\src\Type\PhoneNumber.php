<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/phone_number.proto

namespace Google\Type;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An object representing a phone number, suitable as an API wire format.
 * This representation:
 *  - should not be used for locale-specific formatting of a phone number, such
 *    as "+**************** ext. 123"
 *  - is not designed for efficient storage
 *  - may not be suitable for dialing - specialized libraries (see references)
 *    should be used to parse the number for that purpose
 * To do something meaningful with this number, such as format it for various
 * use-cases, convert it to an `i18n.phonenumbers.PhoneNumber` object first.
 * For instance, in Java this would be:
 *    com.google.type.PhoneNumber wireProto =
 *        com.google.type.PhoneNumber.newBuilder().build();
 *    com.google.i18n.phonenumbers.Phonenumber.PhoneNumber phoneNumber =
 *        PhoneNumberUtil.getInstance().parse(wireProto.getE164Number(), "ZZ");
 *    if (!wireProto.getExtension().isEmpty()) {
 *      phoneNumber.setExtension(wireProto.getExtension());
 *    }
 *  Reference(s):
 *   - https://github.com/google/libphonenumber
 *
 * Generated from protobuf message <code>google.type.PhoneNumber</code>
 */
class PhoneNumber extends \Google\Protobuf\Internal\Message
{
    /**
     * The phone number's extension. The extension is not standardized in ITU
     * recommendations, except for being defined as a series of numbers with a
     * maximum length of 40 digits. Other than digits, some other dialing
     * characters such as ',' (indicating a wait) or '#' may be stored here.
     * Note that no regions currently use extensions with short codes, so this
     * field is normally only set in conjunction with an E.164 number. It is held
     * separately from the E.164 number to allow for short code extensions in the
     * future.
     *
     * Generated from protobuf field <code>string extension = 3;</code>
     */
    protected $extension = '';
    protected $kind;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $e164_number
     *           The phone number, represented as a leading plus sign ('+'), followed by a
     *           phone number that uses a relaxed ITU E.164 format consisting of the
     *           country calling code (1 to 3 digits) and the subscriber number, with no
     *           additional spaces or formatting, e.g.:
     *            - correct: "+15552220123"
     *            - incorrect: "+1 (555) 222-01234 x123".
     *           The ITU E.164 format limits the latter to 12 digits, but in practice not
     *           all countries respect that, so we relax that restriction here.
     *           National-only numbers are not allowed.
     *           References:
     *            - https://www.itu.int/rec/T-REC-E.164-201011-I
     *            - https://en.wikipedia.org/wiki/E.164.
     *            - https://en.wikipedia.org/wiki/List_of_country_calling_codes
     *     @type \Google\Type\PhoneNumber\ShortCode $short_code
     *           A short code.
     *           Reference(s):
     *            - https://en.wikipedia.org/wiki/Short_code
     *     @type string $extension
     *           The phone number's extension. The extension is not standardized in ITU
     *           recommendations, except for being defined as a series of numbers with a
     *           maximum length of 40 digits. Other than digits, some other dialing
     *           characters such as ',' (indicating a wait) or '#' may be stored here.
     *           Note that no regions currently use extensions with short codes, so this
     *           field is normally only set in conjunction with an E.164 number. It is held
     *           separately from the E.164 number to allow for short code extensions in the
     *           future.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Type\PhoneNumber::initOnce();
        parent::__construct($data);
    }

    /**
     * The phone number, represented as a leading plus sign ('+'), followed by a
     * phone number that uses a relaxed ITU E.164 format consisting of the
     * country calling code (1 to 3 digits) and the subscriber number, with no
     * additional spaces or formatting, e.g.:
     *  - correct: "+15552220123"
     *  - incorrect: "+1 (555) 222-01234 x123".
     * The ITU E.164 format limits the latter to 12 digits, but in practice not
     * all countries respect that, so we relax that restriction here.
     * National-only numbers are not allowed.
     * References:
     *  - https://www.itu.int/rec/T-REC-E.164-201011-I
     *  - https://en.wikipedia.org/wiki/E.164.
     *  - https://en.wikipedia.org/wiki/List_of_country_calling_codes
     *
     * Generated from protobuf field <code>string e164_number = 1;</code>
     * @return string
     */
    public function getE164Number()
    {
        return $this->readOneof(1);
    }

    public function hasE164Number()
    {
        return $this->hasOneof(1);
    }

    /**
     * The phone number, represented as a leading plus sign ('+'), followed by a
     * phone number that uses a relaxed ITU E.164 format consisting of the
     * country calling code (1 to 3 digits) and the subscriber number, with no
     * additional spaces or formatting, e.g.:
     *  - correct: "+15552220123"
     *  - incorrect: "+1 (555) 222-01234 x123".
     * The ITU E.164 format limits the latter to 12 digits, but in practice not
     * all countries respect that, so we relax that restriction here.
     * National-only numbers are not allowed.
     * References:
     *  - https://www.itu.int/rec/T-REC-E.164-201011-I
     *  - https://en.wikipedia.org/wiki/E.164.
     *  - https://en.wikipedia.org/wiki/List_of_country_calling_codes
     *
     * Generated from protobuf field <code>string e164_number = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setE164Number($var)
    {
        GPBUtil::checkString($var, True);
        $this->writeOneof(1, $var);

        return $this;
    }

    /**
     * A short code.
     * Reference(s):
     *  - https://en.wikipedia.org/wiki/Short_code
     *
     * Generated from protobuf field <code>.google.type.PhoneNumber.ShortCode short_code = 2;</code>
     * @return \Google\Type\PhoneNumber\ShortCode|null
     */
    public function getShortCode()
    {
        return $this->readOneof(2);
    }

    public function hasShortCode()
    {
        return $this->hasOneof(2);
    }

    /**
     * A short code.
     * Reference(s):
     *  - https://en.wikipedia.org/wiki/Short_code
     *
     * Generated from protobuf field <code>.google.type.PhoneNumber.ShortCode short_code = 2;</code>
     * @param \Google\Type\PhoneNumber\ShortCode $var
     * @return $this
     */
    public function setShortCode($var)
    {
        GPBUtil::checkMessage($var, \Google\Type\PhoneNumber\ShortCode::class);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * The phone number's extension. The extension is not standardized in ITU
     * recommendations, except for being defined as a series of numbers with a
     * maximum length of 40 digits. Other than digits, some other dialing
     * characters such as ',' (indicating a wait) or '#' may be stored here.
     * Note that no regions currently use extensions with short codes, so this
     * field is normally only set in conjunction with an E.164 number. It is held
     * separately from the E.164 number to allow for short code extensions in the
     * future.
     *
     * Generated from protobuf field <code>string extension = 3;</code>
     * @return string
     */
    public function getExtension()
    {
        return $this->extension;
    }

    /**
     * The phone number's extension. The extension is not standardized in ITU
     * recommendations, except for being defined as a series of numbers with a
     * maximum length of 40 digits. Other than digits, some other dialing
     * characters such as ',' (indicating a wait) or '#' may be stored here.
     * Note that no regions currently use extensions with short codes, so this
     * field is normally only set in conjunction with an E.164 number. It is held
     * separately from the E.164 number to allow for short code extensions in the
     * future.
     *
     * Generated from protobuf field <code>string extension = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setExtension($var)
    {
        GPBUtil::checkString($var, True);
        $this->extension = $var;

        return $this;
    }

    /**
     * @return string
     */
    public function getKind()
    {
        return $this->whichOneof("kind");
    }

}

