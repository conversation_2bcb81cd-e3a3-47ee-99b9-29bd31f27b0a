<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/resource_policy_member.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Output-only policy member strings of a Google Cloud resource's built-in
 * identity.
 *
 * Generated from protobuf message <code>google.iam.v1.ResourcePolicyMember</code>
 */
class ResourcePolicyMember extends \Google\Protobuf\Internal\Message
{
    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * user-assigned name (https://google.aip.dev/122). If a resource is deleted
     * and recreated with the same name, the binding will be applicable to the new
     * resource.
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/name/locations/us-central1-a/parameters/my-parameter`
     *
     * Generated from protobuf field <code>string iam_policy_name_principal = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $iam_policy_name_principal = '';
    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * system-assigned unique identifier (https://google.aip.dev/148#uid). If a
     * resource is deleted and recreated with the same name, the binding will not
     * be applicable to the new resource
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/uid/locations/us-central1-a/parameters/a918fed5`
     *
     * Generated from protobuf field <code>string iam_policy_uid_principal = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $iam_policy_uid_principal = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $iam_policy_name_principal
     *           IAM policy binding member referring to a Google Cloud resource by
     *           user-assigned name (https://google.aip.dev/122). If a resource is deleted
     *           and recreated with the same name, the binding will be applicable to the new
     *           resource.
     *           Example:
     *           `principal://parametermanager.googleapis.com/projects/12345/name/locations/us-central1-a/parameters/my-parameter`
     *     @type string $iam_policy_uid_principal
     *           IAM policy binding member referring to a Google Cloud resource by
     *           system-assigned unique identifier (https://google.aip.dev/148#uid). If a
     *           resource is deleted and recreated with the same name, the binding will not
     *           be applicable to the new resource
     *           Example:
     *           `principal://parametermanager.googleapis.com/projects/12345/uid/locations/us-central1-a/parameters/a918fed5`
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\ResourcePolicyMember::initOnce();
        parent::__construct($data);
    }

    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * user-assigned name (https://google.aip.dev/122). If a resource is deleted
     * and recreated with the same name, the binding will be applicable to the new
     * resource.
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/name/locations/us-central1-a/parameters/my-parameter`
     *
     * Generated from protobuf field <code>string iam_policy_name_principal = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getIamPolicyNamePrincipal()
    {
        return $this->iam_policy_name_principal;
    }

    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * user-assigned name (https://google.aip.dev/122). If a resource is deleted
     * and recreated with the same name, the binding will be applicable to the new
     * resource.
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/name/locations/us-central1-a/parameters/my-parameter`
     *
     * Generated from protobuf field <code>string iam_policy_name_principal = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setIamPolicyNamePrincipal($var)
    {
        GPBUtil::checkString($var, True);
        $this->iam_policy_name_principal = $var;

        return $this;
    }

    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * system-assigned unique identifier (https://google.aip.dev/148#uid). If a
     * resource is deleted and recreated with the same name, the binding will not
     * be applicable to the new resource
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/uid/locations/us-central1-a/parameters/a918fed5`
     *
     * Generated from protobuf field <code>string iam_policy_uid_principal = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getIamPolicyUidPrincipal()
    {
        return $this->iam_policy_uid_principal;
    }

    /**
     * IAM policy binding member referring to a Google Cloud resource by
     * system-assigned unique identifier (https://google.aip.dev/148#uid). If a
     * resource is deleted and recreated with the same name, the binding will not
     * be applicable to the new resource
     * Example:
     * `principal://parametermanager.googleapis.com/projects/12345/uid/locations/us-central1-a/parameters/a918fed5`
     *
     * Generated from protobuf field <code>string iam_policy_uid_principal = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setIamPolicyUidPrincipal($var)
    {
        GPBUtil::checkString($var, True);
        $this->iam_policy_uid_principal = $var;

        return $this;
    }

}

