<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: grpc_gcp.proto

namespace GPBMetadata;

class GrpcGcp
{
    public static $is_initialized = false;

    public static function initOnce()
    {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
            return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac9030a0e677270635f6763702e70726f746f1208677270632e67637022" .
            "670a09417069436f6e66696712310a0c6368616e6e656c5f706f6f6c1802" .
            "2001280b321b2e677270632e6763702e4368616e6e656c506f6f6c436f6e" .
            "66696712270a066d6574686f6418e9072003280b32162e677270632e6763" .
            "702e4d6574686f64436f6e66696722690a114368616e6e656c506f6f6c43" .
            "6f6e66696712100a086d61785f73697a6518012001280d12140a0c69646c" .
            "655f74696d656f7574180220012804122c0a246d61785f636f6e63757272" .
            "656e745f73747265616d735f6c6f775f77617465726d61726b1803200128" .
            "0d22490a0c4d6574686f64436f6e666967120c0a046e616d651801200328" .
            "09122b0a08616666696e69747918e9072001280b32182e677270632e6763" .
            "702e416666696e697479436f6e6669672285010a0e416666696e69747943" .
            "6f6e66696712310a07636f6d6d616e6418022001280e32202e677270632e" .
            "6763702e416666696e697479436f6e6669672e436f6d6d616e6412140a0c" .
            "616666696e6974795f6b6579180320012809222a0a07436f6d6d616e6412" .
            "090a05424f554e44100012080a0442494e441001120a0a06554e42494e44" .
            "1002620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}
