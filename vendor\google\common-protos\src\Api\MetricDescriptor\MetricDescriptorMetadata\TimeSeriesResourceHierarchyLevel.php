<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace Google\Api\MetricDescriptor\MetricDescriptorMetadata;

use UnexpectedValueException;

/**
 * The resource hierarchy level of the timeseries data of a metric.
 *
 * Protobuf type <code>google.api.MetricDescriptor.MetricDescriptorMetadata.TimeSeriesResourceHierarchyLevel</code>
 */
class TimeSeriesResourceHierarchyLevel
{
    /**
     * Do not use this default value.
     *
     * Generated from protobuf enum <code>TIME_SERIES_RESOURCE_HIERARCHY_LEVEL_UNSPECIFIED = 0;</code>
     */
    const TIME_SERIES_RESOURCE_HIERARCHY_LEVEL_UNSPECIFIED = 0;
    /**
     * Scopes a metric to a project.
     *
     * Generated from protobuf enum <code>PROJECT = 1;</code>
     */
    const PROJECT = 1;
    /**
     * Scopes a metric to an organization.
     *
     * Generated from protobuf enum <code>ORGANIZATION = 2;</code>
     */
    const ORGANIZATION = 2;
    /**
     * Scopes a metric to a folder.
     *
     * Generated from protobuf enum <code>FOLDER = 3;</code>
     */
    const FOLDER = 3;

    private static $valueToName = [
        self::TIME_SERIES_RESOURCE_HIERARCHY_LEVEL_UNSPECIFIED => 'TIME_SERIES_RESOURCE_HIERARCHY_LEVEL_UNSPECIFIED',
        self::PROJECT => 'PROJECT',
        self::ORGANIZATION => 'ORGANIZATION',
        self::FOLDER => 'FOLDER',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


