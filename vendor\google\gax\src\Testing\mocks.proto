syntax = "proto3";

package google.apicore.testing;

import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

option php_namespace = "Google\\ApiCore\\Testing";
option php_metadata_namespace = "GPBMetadata\\ApiCore\\Testing";

message MockRequest {
  string page_token = 1;
  uint64 page_size = 2;
}

message MockResponse {
  string name = 1;
  uint64 number = 2;
  repeated string resources_list = 3;
  string next_page_token = 4;
  map<string, string> resources_map = 5;
}

message MockRequestBody {
  string name = 1;
  uint64 number = 2;
  repeated string repeated_field = 3;
  MockRequestBody nested_message = 4;
  google.protobuf.BytesValue bytes_value = 5;
  google.protobuf.Duration duration_value = 6;
  google.protobuf.FieldMask field_mask = 7;
  google.protobuf.Int64Value int64_value = 8;
  google.protobuf.ListValue list_value = 9;
  google.protobuf.StringValue string_value = 10;
  google.protobuf.Struct struct_value = 11;
  google.protobuf.Timestamp timestamp_value = 12;
  google.protobuf.Value value_value = 13;
  oneof oneof_field {
    string field_1 = 14;
    string field_2 = 15;
    string field_3 = 16;
  }
}
