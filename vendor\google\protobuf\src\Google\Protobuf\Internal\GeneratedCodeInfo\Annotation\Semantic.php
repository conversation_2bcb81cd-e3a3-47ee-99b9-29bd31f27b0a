<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\GeneratedCodeInfo\Annotation;

use UnexpectedValueException;

/**
 * Represents the identified object's effect on the element in the original
 * .proto file.
 *
 * Protobuf type <code>google.protobuf.GeneratedCodeInfo.Annotation.Semantic</code>
 */
class Semantic
{
    /**
     * There is no effect or the effect is indescribable.
     *
     * Generated from protobuf enum <code>NONE = 0;</code>
     */
    const NONE = 0;
    /**
     * The element is set or otherwise mutated.
     *
     * Generated from protobuf enum <code>SET = 1;</code>
     */
    const SET = 1;
    /**
     * An alias to the element is returned.
     *
     * Generated from protobuf enum <code>ALIAS = 2;</code>
     */
    const ALIAS = 2;

    private static $valueToName = [
        self::NONE => 'NONE',
        self::SET => 'SET',
        self::ALIAS => 'ALIAS',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

