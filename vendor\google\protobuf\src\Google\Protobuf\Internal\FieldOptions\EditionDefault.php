<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FieldOptions;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\GPBWire;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\InputStream;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.protobuf.FieldOptions.EditionDefault</code>
 */
class EditionDefault extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>optional .google.protobuf.Edition edition = 3;</code>
     */
    protected $edition = null;
    /**
     * Textproto value.
     *
     * Generated from protobuf field <code>optional string value = 2;</code>
     */
    protected $value = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $edition
     *     @type string $value
     *           Textproto value.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\Internal\Descriptor::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>optional .google.protobuf.Edition edition = 3;</code>
     * @return int
     */
    public function getEdition()
    {
        return isset($this->edition) ? $this->edition : 0;
    }

    public function hasEdition()
    {
        return isset($this->edition);
    }

    public function clearEdition()
    {
        unset($this->edition);
    }

    /**
     * Generated from protobuf field <code>optional .google.protobuf.Edition edition = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setEdition($var)
    {
        GPBUtil::checkEnum($var, \Google\Protobuf\Internal\Edition::class);
        $this->edition = $var;

        return $this;
    }

    /**
     * Textproto value.
     *
     * Generated from protobuf field <code>optional string value = 2;</code>
     * @return string
     */
    public function getValue()
    {
        return isset($this->value) ? $this->value : '';
    }

    public function hasValue()
    {
        return isset($this->value);
    }

    public function clearValue()
    {
        unset($this->value);
    }

    /**
     * Textproto value.
     *
     * Generated from protobuf field <code>optional string value = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->value = $var;

        return $this;
    }

}

