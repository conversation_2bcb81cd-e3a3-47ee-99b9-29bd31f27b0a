<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FieldOptions;

use UnexpectedValueException;

/**
 * If set to RETENTION_SOURCE, the option will be omitted from the binary.
 *
 * Protobuf type <code>google.protobuf.FieldOptions.OptionRetention</code>
 */
class OptionRetention
{
    /**
     * Generated from protobuf enum <code>RETENTION_UNKNOWN = 0;</code>
     */
    const RETENTION_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>RETENTION_RUNTIME = 1;</code>
     */
    const RETENTION_RUNTIME = 1;
    /**
     * Generated from protobuf enum <code>RETENTION_SOURCE = 2;</code>
     */
    const RETENTION_SOURCE = 2;

    private static $valueToName = [
        self::RETENTION_UNKNOWN => 'RETENTION_UNKNOWN',
        self::RETENTION_RUNTIME => 'RETENTION_RUNTIME',
        self::RETENTION_SOURCE => 'RETENTION_SOURCE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

