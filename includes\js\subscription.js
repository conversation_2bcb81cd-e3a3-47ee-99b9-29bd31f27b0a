import { getToken, onMessage } from "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js";
import { messagingPromise } from "./firebase-init.js";

const verifyNonce = (action) => {
    if (!q_ajax_object?.nonce) {
        console.error('Security token not found');
        return false;
    }
    return true;
};

// Add modal HTML to the page when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Add modal HTML to body
    const modalHTML = `
        <div id="q-notification-modal" class="q-modal">
            <div class="q-modal-content">
                <div class="q-modal-header">
                    <i class="bi bi-bell-fill"></i>
                    <h3>Enable Notifications</h3>
                </div>
                <div class="q-modal-body">
                    <p>Would you like to receive notifications for important updates?</p>
                    <p class="q-modal-description">Stay informed about:</p>
                    <ul>
                        <li><i class="bi bi-check-circle-fill"></i> Important announcements</li>
                        <li><i class="bi bi-check-circle-fill"></i> New features and updates</li>
                        <li><i class="bi bi-check-circle-fill"></i> Time-sensitive information</li>
                    </ul>
                </div>
                <div class="q-modal-footer">
                    <button id="q-modal-cancel" class="q-modal-btn q-modal-btn-cancel">No, thanks</button>
                    <button id="q-modal-accept" class="q-modal-btn q-modal-btn-accept">Enable Notifications</button>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add this modal HTML right after the first modal HTML
    const blockedModalHTML = `
        <div id="q-blocked-modal" class="q-modal">
            <div class="q-modal-content">
                <div class="q-modal-header">
                    <i class="bi bi-bell-slash-fill" style="color: #0384c6;"></i>
                    <h3>Notifications Blocked</h3>
                </div>
                <div class="q-modal-body">
                    <p>You've blocked notifications for this site. To enable them, follow these steps:</p>
                    <ol class="q-blocked-steps">
                        <li>
                            <i class="bi bi-1-circle-fill"></i>
                            <span>Click the lock/info icon <i class="bi bi-lock-fill"></i> in your browser's address bar</span>
                        </li>
                        <li>
                            <i class="bi bi-2-circle-fill"></i>
                            <span>Find "Notifications" in site settings</span>
                        </li>
                        <li>
                            <i class="bi bi-3-circle-fill"></i>
                            <span>Change the setting to "Allow"</span>
                        </li>
                        <li>
                            <i class="bi bi-4-circle-fill"></i>
                            <span>Refresh this page</span>
                        </li>
                    </ol>
                </div>
                <div class="q-modal-footer">
                    <button id="q-blocked-modal-close" class="q-modal-btn q-modal-btn-accept">Got it</button>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', blockedModalHTML);

    // Add modal styles
    const styles = `
        .q-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;

            /* Center modal content */
            display: none;
            justify-content: center;
            align-items: center;
        }

        .q-modal.show {
            display: flex;
        }

        .q-modal-content {
            position: relative;
            background: #fff;
            width: 90%;
            max-width: 500px;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transform: translateY(-20px);
            animation: slideIn 0.3s ease-out forwards;

            /* Prevent margin collapse */
            margin: 0;
        }

        .q-modal-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .q-modal-header i {
            font-size: 24px;
            color: #0384c6;
        }

        .q-modal-header h3 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .q-modal-body {
            margin-bottom: 24px;
        }

        .q-modal-description {
            margin: 16px 0 8px;
            color: #666;
        }

        .q-modal-body ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .q-modal-body li {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 8px 0;
            color: #555;
        }

        .q-modal-body li i {
            color: #0384c6;
        }

        .q-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .q-modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .q-modal-btn-cancel {
            background: #f5f5f5;
            color: #666;
        }

        .q-modal-btn-cancel:hover {
            background: #eee;
        }

        .q-modal-btn-accept {
            background: #0384c6;
            color: white;
        }

        .q-modal-btn-accept:hover {
            background: #0369a1;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
            }
        }

        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    `;

    const additionalStyles = `
        .q-blocked-steps {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .q-blocked-steps li {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 16px 0;
            color: #555;
        }

        .q-blocked-steps li i {
            font-size: 20px;
            color: #0384c6;
        }

        .q-blocked-steps li i.bi-lock-fill {
            font-size: 16px;
            color: #666;
        }

        .q-blocked-steps li span {
            flex: 1;
        }
    `;

    const styleSheet = document.createElement("style");
    styleSheet.textContent = styles + additionalStyles;
    document.head.appendChild(styleSheet);

    const subscribeButton = document.getElementById('q-subscribe-button');
    if (!subscribeButton) return;

    // Wait for messaging to be initialized
    messagingPromise.then(async messaging => {
        async function checkSubscriptionStatus() {
            try {
                // Only check if permission is already granted
                if (Notification.permission === 'granted') {
                    const currentToken = await getToken(messaging, {
                        vapidKey: window.q_firebase_config.publicVapidKey
                    });

                    if (currentToken) {
                        // Verify token is still valid on server
                        const response = await jQuery.ajax({
                            url: q_ajax_object.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'q_verify_subscription',
                                token: currentToken,
                                nonce: q_ajax_object.nonce
                            }
                        });

                        if (response.success && response.data.isValid) {
                            // Hide the button completely when user is already subscribed
                            subscribeButton.style.display = 'none';
                            return true;
                        } else {
                            // Token is invalid or not found on server
                            subscribeButton.innerHTML = '<i class="bi bi-bell-fill"></i> Subscribe to Notifications';
                            subscribeButton.disabled = false;
                            subscribeButton.style.display = 'inline-block'; // Ensure button is visible
                            return false;
                        }
                    }
                }
                subscribeButton.innerHTML = '<i class="bi bi-bell-fill"></i> Subscribe to Notifications';
                subscribeButton.disabled = false;
                subscribeButton.style.display = 'inline-block'; // Ensure button is visible
                return false;
            } catch (err) {
                console.error('Error checking subscription status:', err);
                subscribeButton.innerHTML = '<i class="bi bi-bell-fill"></i> Subscribe to Notifications';
                subscribeButton.disabled = false;
                subscribeButton.style.display = 'inline-block'; // Ensure button is visible
                return false;
            }
        }

        function handlePermissionState() {
            if (Notification.permission === 'denied') {
                subscribeButton.disabled = true;
                subscribeButton.innerHTML = '<i class="bi bi-bell-slash-fill"></i> Notifications Blocked';

                showModal('q-blocked-modal');

                const blockedModal = document.getElementById('q-blocked-modal');
                const closeBtn = document.getElementById('q-blocked-modal-close');

                const closeBlockedModal = () => {
                    hideModal('q-blocked-modal');
                    closeBtn.removeEventListener('click', closeBlockedModal);
                    blockedModal.removeEventListener('click', handleOutsideClick);
                };

                const handleOutsideClick = (event) => {
                    if (event.target === blockedModal) {
                        closeBlockedModal();
                    }
                };

                closeBtn.addEventListener('click', closeBlockedModal);
                blockedModal.addEventListener('click', handleOutsideClick);

                return false;
            }
            return true;
        }

        // Only check if already granted
        if (Notification.permission === 'granted') {
            await checkSubscriptionStatus();
        }

        // Subscribe button click event
        subscribeButton.addEventListener('click', async function() {
            showModal('q-notification-modal');

            const modal = document.getElementById('q-notification-modal');
            const acceptBtn = document.getElementById('q-modal-accept');
            const cancelBtn = document.getElementById('q-modal-cancel');

            const closeModal = () => {
                hideModal('q-notification-modal');
                acceptBtn.removeEventListener('click', handleAccept);
                cancelBtn.removeEventListener('click', handleCancel);
            };

            const handleAccept = async () => {
                closeModal();
                subscribeButton.disabled = true;
                subscribeButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

                try {
                    const token = await getToken(messaging, {
                        vapidKey: window.q_firebase_config.publicVapidKey
                    });

                    if (!token) {
                        throw new Error('Failed to get notification token');
                    }

                    await sendTokenToServer(token);

                    // Show success notification
                    showNotification('success', 'Notifications enabled successfully!');
                    // Hide the button completely after successful subscription
                    subscribeButton.style.display = 'none';

                } catch (err) {
                    console.error('Subscription error:', err);
                    subscribeButton.disabled = false;
                    subscribeButton.innerHTML = '<i class="bi bi-bell-fill"></i> Subscribe to Notifications';
                    subscribeButton.style.display = 'inline-block'; // Ensure button is visible

                    // Show error notification with cleaned up message
                    const errorMessage = err.message === '[object Object]'
                        ? 'Failed to enable notifications'
                        : err.message;

                    showNotification('error', errorMessage);
                    handlePermissionState();
                }
            };

            const handleCancel = () => {
                closeModal();
                subscribeButton.disabled = false;
            };

            acceptBtn.addEventListener('click', handleAccept);
            cancelBtn.addEventListener('click', handleCancel);
        });

        // Handle incoming messages
        onMessage(messaging, (payload) => {
            console.log('Message received. ', payload);

            // Extract notification data
            const { data } = payload;
            if (!data?.title) {
                console.error('Invalid notification payload');
                return;
            }

            // Create notification options
            const notificationOptions = {
                body: data.body,
                icon: data.icon || '/favicon.ico',
                image: data.image,
                data: {
                    url: data.click_action
                }
            };

            // Show notification using the Notifications API
            if (Notification.permission === 'granted') {
                navigator.serviceWorker.ready.then(registration => {
                    registration.showNotification(data.title, notificationOptions);
                });
            }
        });
    }).catch(err => {
        console.error('Failed to initialize messaging:', err);
        if (subscribeButton) {
            subscribeButton.disabled = true;
            subscribeButton.textContent = 'Notifications Not Available';
        }
    });
});

// Helper function to send token to server
async function sendTokenToServer(token) {
    if (!verifyNonce('q_subscribe')) return;

    try {
        const response = await jQuery.ajax({
            url: q_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'q_subscribe',
                token: token,
                nonce: q_ajax_object.nonce,
                security: q_ajax_object.nonce
            },
            dataType: 'json'
        });

        if (!response || typeof response !== 'object') {
            throw new Error('Invalid server response format');
        }

        if (!response.success) {
            throw new Error(response.data?.message || 'Subscription failed');
        }

        return response;
    } catch (error) {
        console.error('Error sending token to server:', error);

        // Handle different types of errors
        let errorMessage;

        if (error.responseJSON) {
            // Server returned an error response
            errorMessage = error.responseJSON.data?.message || error.responseJSON.message;
        } else if (error.responseText) {
            // Try to parse response text as JSON
            try {
                const parsedError = JSON.parse(error.responseText);
                errorMessage = parsedError.data?.message || parsedError.message;
            } catch (e) {
                errorMessage = error.responseText;
            }
        } else if (error.message) {
            // Use error message directly
            errorMessage = error.message;
        } else {
            // Fallback error message
            errorMessage = 'Failed to subscribe to notifications';
        }

        throw new Error(errorMessage);
    }
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
}

// Add a helper function to show notifications to users
function showNotification(type, message) {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.q-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notificationDiv = document.createElement('div');
    notificationDiv.className = `q-notification q-notification-${type}`;

    // Sanitize the message by escaping HTML
    const sanitizedMessage = message
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

    notificationDiv.innerHTML = `
        <div class="q-notification-content">
            <i class="bi ${type === 'error' ? 'bi-exclamation-circle' : 'bi-check-circle'}"></i>
            <span>${sanitizedMessage}</span>
        </div>
    `;

    document.body.appendChild(notificationDiv);

    // Add click-to-dismiss functionality
    notificationDiv.addEventListener('click', () => {
        notificationDiv.remove();
    });

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
            notificationDiv.remove();
        }
    }, 5000);
}
