<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Required information for every language.
 *
 * Generated from protobuf message <code>google.api.CommonLanguageSettings</code>
 */
class CommonLanguageSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @deprecated
     */
    protected $reference_docs_uri = '';
    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     */
    private $destinations;
    /**
     * Configuration for which RPCs should be generated in the GAPIC client.
     *
     * Generated from protobuf field <code>.google.api.SelectiveGapicGeneration selective_gapic_generation = 3;</code>
     */
    protected $selective_gapic_generation = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $reference_docs_uri
     *           Link to automatically generated reference documentation.  Example:
     *           https://cloud.google.com/nodejs/docs/reference/asset/latest
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $destinations
     *           The destination where API teams want this client library to be published.
     *     @type \Google\Api\SelectiveGapicGeneration $selective_gapic_generation
     *           Configuration for which RPCs should be generated in the GAPIC client.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @return string
     * @deprecated
     */
    public function getReferenceDocsUri()
    {
        @trigger_error('reference_docs_uri is deprecated.', E_USER_DEPRECATED);
        return $this->reference_docs_uri;
    }

    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @param string $var
     * @return $this
     * @deprecated
     */
    public function setReferenceDocsUri($var)
    {
        @trigger_error('reference_docs_uri is deprecated.', E_USER_DEPRECATED);
        GPBUtil::checkString($var, True);
        $this->reference_docs_uri = $var;

        return $this;
    }

    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDestinations()
    {
        return $this->destinations;
    }

    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDestinations($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Api\ClientLibraryDestination::class);
        $this->destinations = $arr;

        return $this;
    }

    /**
     * Configuration for which RPCs should be generated in the GAPIC client.
     *
     * Generated from protobuf field <code>.google.api.SelectiveGapicGeneration selective_gapic_generation = 3;</code>
     * @return \Google\Api\SelectiveGapicGeneration|null
     */
    public function getSelectiveGapicGeneration()
    {
        return $this->selective_gapic_generation;
    }

    public function hasSelectiveGapicGeneration()
    {
        return isset($this->selective_gapic_generation);
    }

    public function clearSelectiveGapicGeneration()
    {
        unset($this->selective_gapic_generation);
    }

    /**
     * Configuration for which RPCs should be generated in the GAPIC client.
     *
     * Generated from protobuf field <code>.google.api.SelectiveGapicGeneration selective_gapic_generation = 3;</code>
     * @param \Google\Api\SelectiveGapicGeneration $var
     * @return $this
     */
    public function setSelectiveGapicGeneration($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\SelectiveGapicGeneration::class);
        $this->selective_gapic_generation = $var;

        return $this;
    }

}

