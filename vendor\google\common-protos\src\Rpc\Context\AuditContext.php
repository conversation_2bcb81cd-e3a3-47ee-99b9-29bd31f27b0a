<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/audit_context.proto

namespace Google\Rpc\Context;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * `AuditContext` provides information that is needed for audit logging.
 *
 * Generated from protobuf message <code>google.rpc.context.AuditContext</code>
 */
class AuditContext extends \Google\Protobuf\Internal\Message
{
    /**
     * Serialized audit log.
     *
     * Generated from protobuf field <code>bytes audit_log = 1;</code>
     */
    protected $audit_log = '';
    /**
     * An API request message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_request = 2;</code>
     */
    protected $scrubbed_request = null;
    /**
     * An API response message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_response = 3;</code>
     */
    protected $scrubbed_response = null;
    /**
     * Number of scrubbed response items.
     *
     * Generated from protobuf field <code>int32 scrubbed_response_item_count = 4;</code>
     */
    protected $scrubbed_response_item_count = 0;
    /**
     * Audit resource name which is scrubbed.
     *
     * Generated from protobuf field <code>string target_resource = 5;</code>
     */
    protected $target_resource = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $audit_log
     *           Serialized audit log.
     *     @type \Google\Protobuf\Struct $scrubbed_request
     *           An API request message that is scrubbed based on the method annotation.
     *           This field should only be filled if audit_log field is present.
     *           Service Control will use this to assemble a complete log for Cloud Audit
     *           Logs and Google internal audit logs.
     *     @type \Google\Protobuf\Struct $scrubbed_response
     *           An API response message that is scrubbed based on the method annotation.
     *           This field should only be filled if audit_log field is present.
     *           Service Control will use this to assemble a complete log for Cloud Audit
     *           Logs and Google internal audit logs.
     *     @type int $scrubbed_response_item_count
     *           Number of scrubbed response items.
     *     @type string $target_resource
     *           Audit resource name which is scrubbed.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\Context\AuditContext::initOnce();
        parent::__construct($data);
    }

    /**
     * Serialized audit log.
     *
     * Generated from protobuf field <code>bytes audit_log = 1;</code>
     * @return string
     */
    public function getAuditLog()
    {
        return $this->audit_log;
    }

    /**
     * Serialized audit log.
     *
     * Generated from protobuf field <code>bytes audit_log = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setAuditLog($var)
    {
        GPBUtil::checkString($var, False);
        $this->audit_log = $var;

        return $this;
    }

    /**
     * An API request message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_request = 2;</code>
     * @return \Google\Protobuf\Struct|null
     */
    public function getScrubbedRequest()
    {
        return $this->scrubbed_request;
    }

    public function hasScrubbedRequest()
    {
        return isset($this->scrubbed_request);
    }

    public function clearScrubbedRequest()
    {
        unset($this->scrubbed_request);
    }

    /**
     * An API request message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_request = 2;</code>
     * @param \Google\Protobuf\Struct $var
     * @return $this
     */
    public function setScrubbedRequest($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Struct::class);
        $this->scrubbed_request = $var;

        return $this;
    }

    /**
     * An API response message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_response = 3;</code>
     * @return \Google\Protobuf\Struct|null
     */
    public function getScrubbedResponse()
    {
        return $this->scrubbed_response;
    }

    public function hasScrubbedResponse()
    {
        return isset($this->scrubbed_response);
    }

    public function clearScrubbedResponse()
    {
        unset($this->scrubbed_response);
    }

    /**
     * An API response message that is scrubbed based on the method annotation.
     * This field should only be filled if audit_log field is present.
     * Service Control will use this to assemble a complete log for Cloud Audit
     * Logs and Google internal audit logs.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct scrubbed_response = 3;</code>
     * @param \Google\Protobuf\Struct $var
     * @return $this
     */
    public function setScrubbedResponse($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Struct::class);
        $this->scrubbed_response = $var;

        return $this;
    }

    /**
     * Number of scrubbed response items.
     *
     * Generated from protobuf field <code>int32 scrubbed_response_item_count = 4;</code>
     * @return int
     */
    public function getScrubbedResponseItemCount()
    {
        return $this->scrubbed_response_item_count;
    }

    /**
     * Number of scrubbed response items.
     *
     * Generated from protobuf field <code>int32 scrubbed_response_item_count = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setScrubbedResponseItemCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->scrubbed_response_item_count = $var;

        return $this;
    }

    /**
     * Audit resource name which is scrubbed.
     *
     * Generated from protobuf field <code>string target_resource = 5;</code>
     * @return string
     */
    public function getTargetResource()
    {
        return $this->target_resource;
    }

    /**
     * Audit resource name which is scrubbed.
     *
     * Generated from protobuf field <code>string target_resource = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setTargetResource($var)
    {
        GPBUtil::checkString($var, True);
        $this->target_resource = $var;

        return $this;
    }

}

