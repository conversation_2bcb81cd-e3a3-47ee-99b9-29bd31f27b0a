<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Settings for Go client libraries.
 *
 * Generated from protobuf message <code>google.api.GoSettings</code>
 */
class GoSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     */
    protected $common = null;
    /**
     * Map of service names to renamed services. Keys are the package relative
     * service names and values are the name to be used for the service client
     * and call options.
     * publishing:
     *   go_settings:
     *     renamed_services:
     *       Publisher: TopicAdmin
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     */
    private $renamed_services;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\CommonLanguageSettings $common
     *           Some settings.
     *     @type array|\Google\Protobuf\Internal\MapField $renamed_services
     *           Map of service names to renamed services. Keys are the package relative
     *           service names and values are the name to be used for the service client
     *           and call options.
     *           publishing:
     *             go_settings:
     *               renamed_services:
     *                 Publisher: TopicAdmin
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     * @return \Google\Api\CommonLanguageSettings|null
     */
    public function getCommon()
    {
        return $this->common;
    }

    public function hasCommon()
    {
        return isset($this->common);
    }

    public function clearCommon()
    {
        unset($this->common);
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 1;</code>
     * @param \Google\Api\CommonLanguageSettings $var
     * @return $this
     */
    public function setCommon($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\CommonLanguageSettings::class);
        $this->common = $var;

        return $this;
    }

    /**
     * Map of service names to renamed services. Keys are the package relative
     * service names and values are the name to be used for the service client
     * and call options.
     * publishing:
     *   go_settings:
     *     renamed_services:
     *       Publisher: TopicAdmin
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getRenamedServices()
    {
        return $this->renamed_services;
    }

    /**
     * Map of service names to renamed services. Keys are the package relative
     * service names and values are the name to be used for the service client
     * and call options.
     * publishing:
     *   go_settings:
     *     renamed_services:
     *       Publisher: TopicAdmin
     *
     * Generated from protobuf field <code>map<string, string> renamed_services = 2;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setRenamedServices($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->renamed_services = $arr;

        return $this;
    }

}

