<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/extended_operations.proto

namespace Google\Cloud;

use UnexpectedValueException;

/**
 * An enum to be used to mark the essential (for polling) fields in an
 * API-specific Operation object. A custom Operation object may contain many
 * different fields, but only few of them are essential to conduct a successful
 * polling process.
 *
 * Protobuf type <code>google.cloud.OperationResponseMapping</code>
 */
class OperationResponseMapping
{
    /**
     * Do not use.
     *
     * Generated from protobuf enum <code>UNDEFINED = 0;</code>
     */
    const UNDEFINED = 0;
    /**
     * A field in an API-specific (custom) Operation object which carries the same
     * meaning as google.longrunning.Operation.name.
     *
     * Generated from protobuf enum <code>NAME = 1;</code>
     */
    const NAME = 1;
    /**
     * A field in an API-specific (custom) Operation object which carries the same
     * meaning as google.longrunning.Operation.done. If the annotated field is of
     * an enum type, `annotated_field_name == EnumType.DONE` semantics should be
     * equivalent to `Operation.done == true`. If the annotated field is of type
     * boolean, then it should follow the same semantics as Operation.done.
     * Otherwise, a non-empty value should be treated as `Operation.done == true`.
     *
     * Generated from protobuf enum <code>STATUS = 2;</code>
     */
    const STATUS = 2;
    /**
     * A field in an API-specific (custom) Operation object which carries the same
     * meaning as google.longrunning.Operation.error.code.
     *
     * Generated from protobuf enum <code>ERROR_CODE = 3;</code>
     */
    const ERROR_CODE = 3;
    /**
     * A field in an API-specific (custom) Operation object which carries the same
     * meaning as google.longrunning.Operation.error.message.
     *
     * Generated from protobuf enum <code>ERROR_MESSAGE = 4;</code>
     */
    const ERROR_MESSAGE = 4;

    private static $valueToName = [
        self::UNDEFINED => 'UNDEFINED',
        self::NAME => 'NAME',
        self::STATUS => 'STATUS',
        self::ERROR_CODE => 'ERROR_CODE',
        self::ERROR_MESSAGE => 'ERROR_MESSAGE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

