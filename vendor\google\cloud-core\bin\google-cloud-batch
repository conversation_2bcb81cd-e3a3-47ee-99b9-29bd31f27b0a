#!/usr/bin/env php
<?php
/**
 * Copyright 2017 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Cloud\Core\Batch;

$foundAutoloader = false;
$autoloaderCandidates = [
    '/../vendor/autoload.php',     // Git clone of google-cloud-php-core
    '/../../vendor/autoload.php',  // Git clone of google-cloud-php
    '/../../../autoload.php',      // google/cloud-core installation
    '/../../../../autoload.php',   // google/cloud installation
];
foreach ($autoloaderCandidates as $candidate) {
    if (file_exists(__DIR__ . $candidate)) {
        require_once __DIR__ . $candidate;
        $foundAutoloader = true;
        break;
    }
}

if (!$foundAutoloader) {
    die('No autoloader found');
}

function showUsageAndDie()
{
    die("usage: " . __FILE__ . " [daemon|retry] {id}");
}

if (count($argv) < 2) {
    showUsageAndDie();
}

if ($argv[1] === 'daemon') {
    $daemon = new BatchDaemon(__FILE__);
    if (count($argv) == 2) {
        $daemon->run();
    } else {
        $idNum = (int) $argv[2];
        $job = $daemon->job($idNum);
        $job->run();
    }
} elseif ($argv[1] === 'retry') {
    $retry = new Retry();
    $retry->retryAll();
} else {
    showUsageAndDie();
}
