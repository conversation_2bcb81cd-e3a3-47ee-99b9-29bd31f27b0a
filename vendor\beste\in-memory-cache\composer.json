{"name": "beste/in-memory-cache", "description": "A PSR-6 In-Memory cache that can be used as a fallback implementation and/or in tests.", "keywords": ["cache", "psr-6", "beste"], "license": "MIT", "type": "library", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/cache": "^2.0 || ^3.0", "psr/clock": "^1.0"}, "require-dev": {"beste/clock": "^3.0", "beste/php-cs-fixer-config": "^3.2.0", "friendsofphp/php-cs-fixer": "^3.62.0", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan": "^1.11.10", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpunit/phpunit": "^10.5.2 || ^11.3.1", "symfony/var-dumper": "^6.4 || ^7.1.3"}, "provide": {"psr/cache-implementation": "2.0 || 3.0"}, "suggest": {"psr/clock-implementation": "Allows injecting a Clock, for example a frozen clock for testing"}, "autoload": {"psr-4": {"Beste\\Cache\\": "src"}}, "autoload-dev": {"psr-4": {"Beste\\Cache\\Tests\\": "tests"}}, "config": {"allow-plugins": {"phpstan/extension-installer": true}, "sort-packages": true}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "analyze": "@analyse", "cs-fix": "vendor/bin/php-cs-fixer fix --diff --verbose", "test": "vendor/bin/phpunit --testdox", "test-coverage": ["Composer\\Config::disableProcessTimeout", "XDEBUG_MODE=coverage vendor/bin/phpunit --testdox --coverage-html=.build/coverage"], "check": ["@cs-fix", "@analyse", "@test"]}}