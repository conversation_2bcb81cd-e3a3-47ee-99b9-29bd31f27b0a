<?php
/**
 * Q-<PERSON>usher PWA Functionality
 *
 * @package Q-Pusher
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class Q_PWA
 *
 * Handles Progressive Web App functionality for Q-Pusher
 */
class Q_PWA
{
    /**
     * Initialize the PWA functionality
     */
    public static function init()
    {
        // Register hooks
        add_action('wp_head', [self::class, 'add_pwa_meta_tags']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);
        add_action('init', [self::class, 'register_manifest_route']);
        add_action('init', [self::class, 'register_service_worker_route']);
        add_action('activate_plugin', [self::class, 'maybe_flush_rewrite_rules']);

        // Admin hooks
        add_action('admin_init', [self::class, 'check_pwa_requirements']);

        // Add Web Share API support
        add_action('wp_footer', [self::class, 'add_web_share_support']);

        // AJAX handlers for developer tools
        add_action('wp_ajax_q_pwa_clear_cache', [self::class, 'ajax_clear_cache']);
        add_action('wp_ajax_q_pwa_reset_settings', [self::class, 'ajax_reset_settings']);
    }

    /**
     * Add PWA meta tags to the head
     */
    public static function add_pwa_meta_tags()
    {
        // Only add meta tags if PWA is enabled
        if (get_option('q_pwa_enabled', false)) {
            $manifest_url = home_url('/q-manifest.json');
            $theme_color = get_option('q_pwa_theme_color', '#ffffff');
            $apple_touch_icon = get_option('q_pwa_icon_192', '');

            echo '<link rel="manifest" href="' . esc_url($manifest_url) . '">' . "\n";
            echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";
            echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
            echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
            echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
            echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr(get_option('q_pwa_app_name', get_bloginfo('name'))) . '">' . "\n";

            if (!empty($apple_touch_icon)) {
                echo '<link rel="apple-touch-icon" href="' . esc_url($apple_touch_icon) . '">' . "\n";
            }
        }
    }

    /**
     * Enqueue PWA scripts
     */
    public static function enqueue_pwa_scripts()
    {
        // Only enqueue scripts if PWA is enabled
        if (get_option('q_pwa_enabled', false)) {
            wp_enqueue_script(
                'q-pwa-register',
                Q_PLUGIN_URL . 'includes/js/pwa-register.js',
                [],
                '1.0.0',
                true
            );

            // Enqueue Web Share API script if enabled
            if (get_option('q_pwa_enable_web_share', true)) {
                wp_enqueue_script(
                    'q-pwa-web-share',
                    Q_PLUGIN_URL . 'includes/js/pwa-web-share.js',
                    [],
                    '1.0.0',
                    true
                );
            }

            // Pass data to the script
            wp_localize_script('q-pwa-register', 'qPwaData', [
                'serviceWorkerUrl' => home_url('/q-sw.js'),
                'debug' => (defined('WP_DEBUG') && WP_DEBUG) ? true : false,
                // Standard install button settings
                'showInstallButton' => get_option('q_pwa_show_install_button', true),
                'installTitle' => get_option('q_pwa_install_title', 'Install App'),
                'installText' => get_option('q_pwa_install_text', 'Install this app on your device for quick and easy access.'),
                'installButtonText' => get_option('q_pwa_install_button_text', 'Install'),
                'dismissButtonText' => get_option('q_pwa_dismiss_button_text', 'Not Now'),
                'dismissDuration' => get_option('q_pwa_dismiss_duration', 86400000), // Default: 24 hours in milliseconds
                // Floating button settings
                'showFloatingButton' => get_option('q_pwa_show_floating_button', false),
                'floatingButtonPosition' => get_option('q_pwa_floating_button_position', 'bottom-right'),
                'floatingButtonColor' => get_option('q_pwa_floating_button_color', '#0384c6'),
                // Platform-specific instructions
                'showInstructions' => get_option('q_pwa_show_instructions', true),
                'iosInstructions' => get_option('q_pwa_custom_ios_instructions', "To install this app on your iOS device:\n1. Tap the Share icon (rectangle with arrow) at the bottom of the screen\n2. Scroll down and tap 'Add to Home Screen'\n3. Tap 'Add' in the top right corner"),
                'androidInstructions' => get_option('q_pwa_custom_android_instructions', "To install this app on your Android device:\n1. Tap the 'Install' button in the prompt\n2. If no prompt appears, tap the menu icon (three dots) in your browser\n3. Select 'Add to Home screen' or 'Install App'"),
                'desktopInstructions' => get_option('q_pwa_custom_desktop_instructions', "To install this app on your desktop:\n1. Click the install icon in the address bar\n2. Click 'Install' in the prompt that appears\n3. The app will open in a new window")
            ]);
        }
    }

    /**
     * Register manifest route
     */
    public static function register_manifest_route()
    {
        add_rewrite_rule(
            '^q-manifest\.json$',
            'index.php?q_manifest=1',
            'top'
        );

        // Add offline page route
        add_rewrite_rule(
            '^offline/?$',
            'index.php?q_offline=1',
            'top'
        );

        add_filter('query_vars', function ($vars) {
            $vars[] = 'q_manifest';
            $vars[] = 'q_offline';
            return $vars;
        });

        add_action('template_redirect', function () {
            if (get_query_var('q_manifest')) {
                header('Content-Type: application/json');
                header('Cache-Control: no-cache');
                echo self::generate_manifest();
                exit;
            }
        });

        // Also create a physical manifest file as a fallback
        self::create_physical_manifest_file();
    }

    /**
     * Create a physical manifest file in the root directory
     */
    public static function create_physical_manifest_file()
    {
        $manifest_content = self::generate_manifest();
        $manifest_path = ABSPATH . 'q-manifest.json';

        // Debug logging
        error_log('Q-PWA: Attempting to create manifest file at ' . $manifest_path);

        // Check if WordPress root directory is writable
        if (!is_writable(ABSPATH)) {
            error_log('Q-PWA: WordPress root directory is not writable');
            return false;
        }

        // Delete existing manifest if it exists
        if (file_exists($manifest_path)) {
            if (!unlink($manifest_path)) {
                error_log('Q-PWA: Failed to delete existing manifest file');
                return false;
            }
        }

        // Try to write the file
        if (file_put_contents($manifest_path, $manifest_content) !== false) {
            // Set proper permissions
            @chmod($manifest_path, 0644);
            error_log('Q-PWA: Manifest file created successfully');
            return true;
        }

        error_log('Q-PWA: Failed to create manifest file');
        return false;
    }

    /**
     * Register service worker route
     */
    public static function register_service_worker_route()
    {
        add_rewrite_rule(
            '^q-sw\.js$',
            'index.php?q_pwa_sw=1',
            'top'
        );

        add_filter('query_vars', function ($vars) {
            $vars[] = 'q_pwa_sw';
            return $vars;
        });

        add_action('template_redirect', function () {
            if (get_query_var('q_pwa_sw')) {
                header('Content-Type: application/javascript');
                header('Service-Worker-Allowed: /');
                header('Cache-Control: no-cache');

                echo self::generate_service_worker();
                exit;
            }
        });

        // Also create a physical service worker file as a fallback
        self::create_physical_service_worker_file();
    }

    /**
     * Create a physical service worker file in the root directory
     */
    public static function create_physical_service_worker_file()
    {
        $sw_content = self::generate_service_worker();
        $sw_path = ABSPATH . 'q-sw.js';

        // Debug logging
        error_log('Q-PWA: Attempting to create service worker file at ' . $sw_path);

        // Check if WordPress root directory is writable
        if (!is_writable(ABSPATH)) {
            error_log('Q-PWA: WordPress root directory is not writable');
            return false;
        }

        // Delete existing service worker if it exists
        if (file_exists($sw_path)) {
            if (!unlink($sw_path)) {
                error_log('Q-PWA: Failed to delete existing service worker file');
                return false;
            }
        }

        // Try to write the file
        if (file_put_contents($sw_path, $sw_content) !== false) {
            // Set proper permissions
            @chmod($sw_path, 0644);
            error_log('Q-PWA: Service worker file created successfully');
            return true;
        }

        error_log('Q-PWA: Failed to create service worker file');
        return false;
    }

    /**
     * Generate the manifest JSON
     */
    public static function generate_manifest()
    {
        $manifest = [
            'name' => get_option('q_pwa_app_name', get_bloginfo('name')),
            'short_name' => get_option('q_pwa_short_name', get_bloginfo('name')),
            'description' => get_option('q_pwa_description', get_bloginfo('description')),
            'start_url' => home_url('/?utm_source=pwa'),
            'display' => get_option('q_pwa_display', 'standalone'),
            'background_color' => get_option('q_pwa_background_color', '#ffffff'),
            'theme_color' => get_option('q_pwa_theme_color', '#ffffff'),
            'icons' => self::get_icons_array(),
            'orientation' => 'portrait',
            'scope' => home_url('/'),
            'lang' => get_locale(),
            'dir' => is_rtl() ? 'rtl' : 'ltr',
            'prefer_related_applications' => false,
            'shortcuts' => self::get_shortcuts_array(),
            'categories' => self::get_categories_array(),
            'screenshots' => self::get_screenshots_array(),
        ];

        return wp_json_encode($manifest, JSON_PRETTY_PRINT);
    }

    /**
     * Get icons array for manifest
     */
    private static function get_icons_array()
    {
        $icons = [];

        $icon_sizes = [
            '72' => get_option('q_pwa_icon_72', ''),
            '96' => get_option('q_pwa_icon_96', ''),
            '128' => get_option('q_pwa_icon_128', ''),
            '144' => get_option('q_pwa_icon_144', ''),
            '152' => get_option('q_pwa_icon_152', ''),
            '192' => get_option('q_pwa_icon_192', ''),
            '384' => get_option('q_pwa_icon_384', ''),
            '512' => get_option('q_pwa_icon_512', ''),
        ];

        foreach ($icon_sizes as $size => $url) {
            if (!empty($url)) {
                $icons[] = [
                    'src' => $url,
                    'sizes' => $size . 'x' . $size,
                    'type' => 'image/png',
                    'purpose' => 'any maskable'
                ];
            }
        }

        return $icons;
    }

    /**
     * Get shortcuts array for manifest
     */
    private static function get_shortcuts_array()
    {
        $shortcuts = [];

        // Get custom shortcuts from settings
        $custom_shortcuts = get_option('q_pwa_shortcuts', []);

        if (!empty($custom_shortcuts) && is_array($custom_shortcuts)) {
            foreach ($custom_shortcuts as $shortcut) {
                if (!empty($shortcut['name']) && !empty($shortcut['url'])) {
                    $shortcuts[] = [
                        'name' => $shortcut['name'],
                        'short_name' => $shortcut['short_name'] ?? $shortcut['name'],
                        'description' => $shortcut['description'] ?? '',
                        'url' => $shortcut['url'],
                        'icons' => !empty($shortcut['icon']) ? [
                            [
                                'src' => $shortcut['icon'],
                                'sizes' => '96x96',
                                'type' => 'image/png'
                            ]
                        ] : []
                    ];
                }
            }
        }

        // Add default shortcuts if none are configured
        if (empty($shortcuts)) {
            $shortcuts = [
                [
                    'name' => 'Home',
                    'short_name' => 'Home',
                    'description' => 'Go to homepage',
                    'url' => home_url('/'),
                    'icons' => []
                ]
            ];

            // Add notifications shortcut if Q-Notify form exists
            if (class_exists('FrmForm')) {
                global $wpdb;
                $form_exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$wpdb->prefix}frm_forms WHERE name = %s", 'Q-Notify'));
                if ($form_exists) {
                    $shortcuts[] = [
                        'name' => 'Notifications',
                        'short_name' => 'Notify',
                        'description' => 'Send notifications',
                        'url' => home_url('/q-notify/'),
                        'icons' => []
                    ];
                }
            }
        }

        return $shortcuts;
    }

    /**
     * Get categories array for manifest
     */
    private static function get_categories_array()
    {
        $categories = get_option('q_pwa_categories', []);

        if (empty($categories)) {
            // Default categories based on site type
            $categories = ['productivity', 'utilities'];

            // Add more specific categories based on content
            if (function_exists('woocommerce_version')) {
                $categories[] = 'shopping';
            }

            if (class_exists('FrmForm')) {
                $categories[] = 'business';
            }
        }

        return $categories;
    }

    /**
     * Get screenshots array for manifest
     */
    private static function get_screenshots_array()
    {
        $screenshots = [];

        $screenshot_urls = [
            get_option('q_pwa_screenshot_desktop', ''),
            get_option('q_pwa_screenshot_mobile', ''),
        ];

        foreach ($screenshot_urls as $index => $url) {
            if (!empty($url)) {
                $screenshots[] = [
                    'src' => $url,
                    'sizes' => $index === 0 ? '1280x720' : '750x1334',
                    'type' => 'image/png',
                    'form_factor' => $index === 0 ? 'wide' : 'narrow',
                    'label' => $index === 0 ? 'Desktop view' : 'Mobile view'
                ];
            }
        }

        return $screenshots;
    }

    /**
     * Generate the service worker JS
     */
    public static function generate_service_worker()
    {
        $sw_template_path = Q_PLUGIN_DIR . 'includes/pwa/service-worker-template.js';

        if (file_exists($sw_template_path)) {
            $sw_content = file_get_contents($sw_template_path);

            // Get Firebase configuration
            $firebase_config = json_decode(get_option('q_firebase_config', '{}'), true);

            // If JSON config is not available or invalid, try individual settings
            if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
                $firebase_config = array(
                    'apiKey' => get_option('q_firebase_api_key', ''),
                    'authDomain' => get_option('q_firebase_auth_domain', ''),
                    'projectId' => get_option('q_firebase_project_id', ''),
                    'storageBucket' => get_option('q_firebase_storage_bucket', ''),
                    'messagingSenderId' => get_option('q_firebase_messaging_sender_id', ''),
                    'appId' => get_option('q_firebase_app_id', ''),
                );
            }

            // Replace placeholders
            $sw_content = str_replace(
                [
                    '{{CACHE_NAME}}',
                    '{{CACHE_VERSION}}',
                    '{{OFFLINE_PAGE}}',
                    '{{CACHE_URLS}}',
                    '{{FIREBASE_API_KEY}}',
                    '{{FIREBASE_AUTH_DOMAIN}}',
                    '{{FIREBASE_PROJECT_ID}}',
                    '{{FIREBASE_STORAGE_BUCKET}}',
                    '{{FIREBASE_MESSAGING_SENDER_ID}}',
                    '{{FIREBASE_APP_ID}}'
                ],
                [
                    'q-pwa-cache',
                    get_option('q_pwa_cache_version', '1.0.0'),
                    home_url('/offline.html'),
                    self::get_cache_urls_json(),
                    $firebase_config['apiKey'],
                    $firebase_config['authDomain'],
                    $firebase_config['projectId'],
                    $firebase_config['storageBucket'],
                    $firebase_config['messagingSenderId'],
                    $firebase_config['appId']
                ],
                $sw_content
            );

            return $sw_content;
        }

        return '// Service worker template not found';
    }

    /**
     * Get cache URLs as JSON
     */
    private static function get_cache_urls_json()
    {
        // Define a simple array of URLs to cache
        $cache_urls = [
            '/',
            '/offline.html',
            '/offline/index.html',
            '/wp-content/plugins/q-pusher/includes/js/pwa-register.js'
        ];

        // Filter out any duplicate URLs
        $cache_urls = array_unique($cache_urls);

        // Return a properly formatted JSON array with JSON_UNESCAPED_SLASHES to ensure proper URL formatting
        return json_encode($cache_urls, JSON_UNESCAPED_SLASHES);
    }

    /**
     * Flush rewrite rules when plugin is activated
     */
    public static function maybe_flush_rewrite_rules()
    {
        if (get_option('q_pwa_flush_rewrite_rules', false)) {
            flush_rewrite_rules();
            delete_option('q_pwa_flush_rewrite_rules');
        }
    }

    /**
     * Check PWA requirements
     */
    public static function check_pwa_requirements()
    {
        // Check if HTTPS is enabled
        if (get_option('q_pwa_enabled', false) && !is_ssl()) {
            add_action('admin_notices', function () {
                ?>
                <div class="notice notice-warning is-dismissible">
                    <p><?php _e('Q-Pusher PWA requires HTTPS to work properly. Please enable HTTPS on your site.', 'q-pusher'); ?></p>
                </div>
                <?php
            });
        }
    }

    /**
     * Add Web Share API support
     */
    public static function add_web_share_support()
    {
        // Only add if PWA is enabled
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        ?>
        <script>
            // Web Share API Support
            window.qPwaShare = {
                isSupported: function () {
                    return navigator.share !== undefined;
                },

                share: function (data) {
                    if (!this.isSupported()) {
                        console.warn('Web Share API not supported');
                        return false;
                    }

                    const shareData = {
                        title: data.title || document.title,
                        text: data.text || '',
                        url: data.url || window.location.href
                    };

                    return navigator.share(shareData)
                        .then(() => {
                            console.log('Content shared successfully');
                            return true;
                        })
                        .catch((error) => {
                            console.error('Error sharing content:', error);
                            return false;
                        });
                },

                shareCurrentPage: function () {
                    return this.share({
                        title: document.title,
                        text: document.querySelector('meta[name="description"]')?.content || '',
                        url: window.location.href
                    });
                }
            };

            // Add share buttons functionality
            document.addEventListener('DOMContentLoaded', function () {
                // Find all elements with q-pwa-share class
                const shareButtons = document.querySelectorAll('.q-pwa-share, [data-q-share]');

                shareButtons.forEach(button => {
                    // Only show share buttons if Web Share API is supported
                    if (window.qPwaShare.isSupported()) {
                        button.style.display = 'inline-block';

                        button.addEventListener('click', function (e) {
                            e.preventDefault();

                            const shareData = {
                                title: this.dataset.shareTitle || document.title,
                                text: this.dataset.shareText || '',
                                url: this.dataset.shareUrl || window.location.href
                            };

                            window.qPwaShare.share(shareData);
                        });
                    } else {
                        // Hide share buttons if not supported
                        button.style.display = 'none';
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Add PWA analytics tracking
     */
    public static function track_pwa_usage()
    {
        // Only track if PWA is enabled
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Track PWA installation
        if (isset($_GET['utm_source']) && $_GET['utm_source'] === 'pwa') {
            $pwa_installs = get_option('q_pwa_install_count', 0);
            update_option('q_pwa_install_count', $pwa_installs + 1);

            // Track daily installs
            $today = date('Y-m-d');
            $daily_installs = get_option('q_pwa_daily_installs', []);
            $daily_installs[$today] = ($daily_installs[$today] ?? 0) + 1;

            // Keep only last 30 days
            $daily_installs = array_slice($daily_installs, -30, 30, true);
            update_option('q_pwa_daily_installs', $daily_installs);
        }
    }

    /**
     * Get PWA analytics data
     */
    public static function get_pwa_analytics()
    {
        return [
            'total_installs' => get_option('q_pwa_install_count', 0),
            'daily_installs' => get_option('q_pwa_daily_installs', []),
            'last_updated' => get_option('q_pwa_cache_version', '1.0.0'),
            'enabled' => get_option('q_pwa_enabled', false),
        ];
    }

    /**
     * AJAX handler to clear PWA cache
     */
    public static function ajax_clear_cache()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'q_pwa_clear_cache')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // Increment cache version to force cache refresh
            $current_version = get_option('q_pwa_cache_version', '1.0.0');
            $version_parts = explode('.', $current_version);
            $version_parts[2] = (int) $version_parts[2] + 1;
            $new_version = implode('.', $version_parts);

            update_option('q_pwa_cache_version', $new_version);

            // Regenerate service worker and manifest files
            self::create_physical_service_worker_file();
            self::create_physical_manifest_file();

            wp_send_json_success('PWA cache cleared successfully. New version: ' . $new_version);
        } catch (Exception $e) {
            wp_send_json_error('Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler to reset PWA settings
     */
    public static function ajax_reset_settings()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'q_pwa_reset_settings')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // List of all PWA options to reset
            $pwa_options = [
                'q_pwa_enabled',
                'q_pwa_app_name',
                'q_pwa_short_name',
                'q_pwa_description',
                'q_pwa_theme_color',
                'q_pwa_background_color',
                'q_pwa_display',
                'q_pwa_icon_72',
                'q_pwa_icon_96',
                'q_pwa_icon_128',
                'q_pwa_icon_144',
                'q_pwa_icon_152',
                'q_pwa_icon_192',
                'q_pwa_icon_384',
                'q_pwa_icon_512',
                'q_pwa_offline_page',
                'q_pwa_cache_urls',
                'q_pwa_cache_version',
                'q_pwa_show_install_button',
                'q_pwa_install_title',
                'q_pwa_install_text',
                'q_pwa_install_button_text',
                'q_pwa_dismiss_button_text',
                'q_pwa_dismiss_duration',
                'q_pwa_show_floating_button',
                'q_pwa_floating_button_position',
                'q_pwa_floating_button_color',
                'q_pwa_floating_button_icon',
                'q_pwa_show_instructions',
                'q_pwa_custom_ios_instructions',
                'q_pwa_custom_android_instructions',
                'q_pwa_custom_desktop_instructions',
                'q_pwa_shortcuts',
                'q_pwa_categories',
                'q_pwa_screenshot_desktop',
                'q_pwa_screenshot_mobile',
                'q_pwa_enable_background_sync',
                'q_pwa_enable_web_share',
                'q_pwa_enable_analytics',
                'q_pwa_install_count',
                'q_pwa_daily_installs',
            ];

            // Delete all PWA options
            foreach ($pwa_options as $option) {
                delete_option($option);
            }

            // Remove physical files
            $manifest_path = ABSPATH . 'q-manifest.json';
            $sw_path = ABSPATH . 'q-sw.js';

            if (file_exists($manifest_path)) {
                unlink($manifest_path);
            }

            if (file_exists($sw_path)) {
                unlink($sw_path);
            }

            wp_send_json_success('PWA settings reset successfully');
        } catch (Exception $e) {
            wp_send_json_error('Failed to reset settings: ' . $e->getMessage());
        }
    }
}
