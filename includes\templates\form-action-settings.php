<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.'); // Prevent direct access
}

// Initialize variables with default values from options
$title = isset($options['title']) ? sanitize_text_field($options['title']) : '';
$message = isset($options['message']) ? sanitize_textarea_field($options['message']) : '';
$subscribers = isset($options['subscribers']) ? $options['subscribers'] : array();
$notification_icon = isset($options['notification_icon']) ? esc_url($options['notification_icon']) : '';
$notification_url = isset($options['notification_url']) ? esc_url($options['notification_url']) : '';
$notification_image = isset($options['notification_image']) ? esc_url($options['notification_image']) : '';
if (empty($notification_url)) {
    $notification_url = site_url() . '/app';
}
$trigger_delay = isset($options['trigger_delay']) ? intval($options['trigger_delay']) : 0;

// Example of error handling when retrieving users
try {
    $all_subscribers = get_users(array(
        'meta_key' => 'q_push_token',
    ));
} catch (Exception $e) {
    error_log("Error retrieving users: " . $e->getMessage());
    // Handle error, e.g., show a message to the user
}

// Group users by role for better organization
$users_by_role = array();
error_log('All Subscribers before loop: ' . print_r($all_subscribers, true));
foreach ($all_subscribers as $user) {
    if (isset($user->roles) && is_array($user->roles)) {
        foreach ($user->roles as $role) {
            if (!isset($users_by_role[$role])) {
                $users_by_role[$role] = array(); // Initialize role array if not set
            }
            $users_by_role[$role][] = $user; // Add user to the corresponding role
        }
    }
}
error_log('Users by role: ' . print_r($users_by_role, true));
?>

<div class="frm_form_field">
    <div class="frm_section_heading">
        <h2><?php esc_html_e('Push Notification Settings', 'formidable-firebase-push'); ?></h2>
    </div>

    <table class="form-table">
        <tr>
            <th>
                <label><?php esc_html_e('Notification Title', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter the title for your push notification. You can use [field_id] shortcodes.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('title')); ?>"
                    value="<?php echo esc_attr($title); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter notification title', 'formidable-firebase-push'); ?>" />
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Notification Message', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter the message for your push notification. You can use [field_id] shortcodes.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <textarea name="<?php echo esc_attr($this->get_field_name('message')); ?>" class="large-text" rows="5"
                    placeholder="<?php esc_attr_e('Enter notification message', 'formidable-firebase-push'); ?>"><?php echo esc_textarea($message); ?></textarea>
                <p class="description">
                    <?php esc_html_e('Use Formidable field shortcodes like [7] or [field_key] to insert field values. WordPress shortcodes are also supported.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Notification Image', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter an image URL or use field shortcodes. The image will be shown in the notification.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('notification_image')); ?>"
                    value="<?php echo esc_attr($notification_image); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter image URL or shortcode', 'formidable-firebase-push'); ?>" />
                <p class="description">
                    <?php esc_html_e('Enter a direct image URL or use Formidable field shortcodes like [7] to use uploaded images.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Subscriber Emails/Shortcodes', 'formidable-firebase-push'); ?></label>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('subscriber_email')); ?>[]"
                    value="<?php echo esc_attr(isset($options['subscriber_email']) ? implode(',', (array) $options['subscriber_email']) : ''); ?>"
                    class="large-text"
                    placeholder="<?php esc_attr_e('Enter comma-separated emails or shortcodes', 'formidable-firebase-push'); ?>" />
                <p class="description">
                    <?php esc_html_e('Enter the email address of the subscriber to send the notification to. You can use [field_id] shortcodes. To send to multiple emails, separate them with commas.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Retrigger Notifications', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enable to automatically resend notifications if they have not been clicked.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <label>
                    <input type="checkbox" name="<?php echo esc_attr($this->get_field_name('enable_retrigger')); ?>"
                        value="1" <?php checked(isset($options['enable_retrigger']) ? $options['enable_retrigger'] : 0); ?> />
                    <?php esc_html_e('Enable notification retriggering', 'formidable-firebase-push'); ?>
                </label>
                <div style="margin-top: 10px;">
                    <label>
                        <?php esc_html_e('Retrigger after', 'formidable-firebase-push'); ?>
                        <input type="number" name="<?php echo esc_attr($this->get_field_name('retrigger_delay')); ?>"
                            value="<?php echo esc_attr(isset($options['retrigger_delay']) ? intval($options['retrigger_delay']) : 24); ?>"
                            min="1" max="72" style="width: 60px;" />
                        <?php esc_html_e('hours if not clicked', 'formidable-firebase-push'); ?>
                    </label>
                </div>
                <div style="margin-top: 10px;">
                    <label>
                        <?php esc_html_e('Maximum retrigger attempts', 'formidable-firebase-push'); ?>
                        <input type="number"
                            name="<?php echo esc_attr($this->get_field_name('retrigger_max_attempts')); ?>"
                            value="<?php echo esc_attr(isset($options['retrigger_max_attempts']) ? intval($options['retrigger_max_attempts']) : 3); ?>"
                            min="1" max="10" style="width: 60px;" />
                    </label>
                </div>
                <p class="description">
                    <?php esc_html_e('If enabled, notifications will be automatically resent if the user has not clicked them within the specified time period.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>
    </table>
</div>