<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * `Distribution` contains summary statistics for a population of values. It
 * optionally contains a histogram representing the distribution of those values
 * across a set of buckets.
 * The summary statistics are the count, mean, sum of the squared deviation from
 * the mean, the minimum, and the maximum of the set of population of values.
 * The histogram is based on a sequence of buckets and gives a count of values
 * that fall into each bucket. The boundaries of the buckets are given either
 * explicitly or by formulas for buckets of fixed or exponentially increasing
 * widths.
 * Although it is not forbidden, it is generally a bad idea to include
 * non-finite values (infinities or NaNs) in the population of values, as this
 * will render the `mean` and `sum_of_squared_deviation` fields meaningless.
 *
 * Generated from protobuf message <code>google.api.Distribution</code>
 */
class Distribution extends \Google\Protobuf\Internal\Message
{
    /**
     * The number of values in the population. Must be non-negative. This value
     * must equal the sum of the values in `bucket_counts` if a histogram is
     * provided.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     */
    protected $count = 0;
    /**
     * The arithmetic mean of the values in the population. If `count` is zero
     * then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     */
    protected $mean = 0.0;
    /**
     * The sum of squared deviations from the mean of the values in the
     * population. For values x_i this is:
     *     Sum[i=1..n]((x_i - mean)^2)
     * Knuth, "The Art of Computer Programming", Vol. 2, page 232, 3rd edition
     * describes Welford's method for accumulating this sum in one pass.
     * If `count` is zero then this field must be zero.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 3;</code>
     */
    protected $sum_of_squared_deviation = 0.0;
    /**
     * If specified, contains the range of the population values. The field
     * must not be present if the `count` is zero.
     *
     * Generated from protobuf field <code>.google.api.Distribution.Range range = 4;</code>
     */
    protected $range = null;
    /**
     * Defines the histogram bucket boundaries. If the distribution does not
     * contain a histogram, then omit this field.
     *
     * Generated from protobuf field <code>.google.api.Distribution.BucketOptions bucket_options = 6;</code>
     */
    protected $bucket_options = null;
    /**
     * The number of values in each bucket of the histogram, as described in
     * `bucket_options`. If the distribution does not have a histogram, then omit
     * this field. If there is a histogram, then the sum of the values in
     * `bucket_counts` must equal the value in the `count` field of the
     * distribution.
     * If present, `bucket_counts` should contain N values, where N is the number
     * of buckets specified in `bucket_options`. If you supply fewer than N
     * values, the remaining values are assumed to be 0.
     * The order of the values in `bucket_counts` follows the bucket numbering
     * schemes described for the three bucket types. The first value must be the
     * count for the underflow bucket (number 0). The next N-2 values are the
     * counts for the finite buckets (number 1 through N-2). The N'th value in
     * `bucket_counts` is the count for the overflow bucket (number N-1).
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 7;</code>
     */
    private $bucket_counts;
    /**
     * Must be in increasing order of `value` field.
     *
     * Generated from protobuf field <code>repeated .google.api.Distribution.Exemplar exemplars = 10;</code>
     */
    private $exemplars;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $count
     *           The number of values in the population. Must be non-negative. This value
     *           must equal the sum of the values in `bucket_counts` if a histogram is
     *           provided.
     *     @type float $mean
     *           The arithmetic mean of the values in the population. If `count` is zero
     *           then this field must be zero.
     *     @type float $sum_of_squared_deviation
     *           The sum of squared deviations from the mean of the values in the
     *           population. For values x_i this is:
     *               Sum[i=1..n]((x_i - mean)^2)
     *           Knuth, "The Art of Computer Programming", Vol. 2, page 232, 3rd edition
     *           describes Welford's method for accumulating this sum in one pass.
     *           If `count` is zero then this field must be zero.
     *     @type \Google\Api\Distribution\Range $range
     *           If specified, contains the range of the population values. The field
     *           must not be present if the `count` is zero.
     *     @type \Google\Api\Distribution\BucketOptions $bucket_options
     *           Defines the histogram bucket boundaries. If the distribution does not
     *           contain a histogram, then omit this field.
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $bucket_counts
     *           The number of values in each bucket of the histogram, as described in
     *           `bucket_options`. If the distribution does not have a histogram, then omit
     *           this field. If there is a histogram, then the sum of the values in
     *           `bucket_counts` must equal the value in the `count` field of the
     *           distribution.
     *           If present, `bucket_counts` should contain N values, where N is the number
     *           of buckets specified in `bucket_options`. If you supply fewer than N
     *           values, the remaining values are assumed to be 0.
     *           The order of the values in `bucket_counts` follows the bucket numbering
     *           schemes described for the three bucket types. The first value must be the
     *           count for the underflow bucket (number 0). The next N-2 values are the
     *           counts for the finite buckets (number 1 through N-2). The N'th value in
     *           `bucket_counts` is the count for the overflow bucket (number N-1).
     *     @type array<\Google\Api\Distribution\Exemplar>|\Google\Protobuf\Internal\RepeatedField $exemplars
     *           Must be in increasing order of `value` field.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Distribution::initOnce();
        parent::__construct($data);
    }

    /**
     * The number of values in the population. Must be non-negative. This value
     * must equal the sum of the values in `bucket_counts` if a histogram is
     * provided.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     * @return int|string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * The number of values in the population. Must be non-negative. This value
     * must equal the sum of the values in `bucket_counts` if a histogram is
     * provided.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCount($var)
    {
        GPBUtil::checkInt64($var);
        $this->count = $var;

        return $this;
    }

    /**
     * The arithmetic mean of the values in the population. If `count` is zero
     * then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     * @return float
     */
    public function getMean()
    {
        return $this->mean;
    }

    /**
     * The arithmetic mean of the values in the population. If `count` is zero
     * then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setMean($var)
    {
        GPBUtil::checkDouble($var);
        $this->mean = $var;

        return $this;
    }

    /**
     * The sum of squared deviations from the mean of the values in the
     * population. For values x_i this is:
     *     Sum[i=1..n]((x_i - mean)^2)
     * Knuth, "The Art of Computer Programming", Vol. 2, page 232, 3rd edition
     * describes Welford's method for accumulating this sum in one pass.
     * If `count` is zero then this field must be zero.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 3;</code>
     * @return float
     */
    public function getSumOfSquaredDeviation()
    {
        return $this->sum_of_squared_deviation;
    }

    /**
     * The sum of squared deviations from the mean of the values in the
     * population. For values x_i this is:
     *     Sum[i=1..n]((x_i - mean)^2)
     * Knuth, "The Art of Computer Programming", Vol. 2, page 232, 3rd edition
     * describes Welford's method for accumulating this sum in one pass.
     * If `count` is zero then this field must be zero.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setSumOfSquaredDeviation($var)
    {
        GPBUtil::checkDouble($var);
        $this->sum_of_squared_deviation = $var;

        return $this;
    }

    /**
     * If specified, contains the range of the population values. The field
     * must not be present if the `count` is zero.
     *
     * Generated from protobuf field <code>.google.api.Distribution.Range range = 4;</code>
     * @return \Google\Api\Distribution\Range|null
     */
    public function getRange()
    {
        return $this->range;
    }

    public function hasRange()
    {
        return isset($this->range);
    }

    public function clearRange()
    {
        unset($this->range);
    }

    /**
     * If specified, contains the range of the population values. The field
     * must not be present if the `count` is zero.
     *
     * Generated from protobuf field <code>.google.api.Distribution.Range range = 4;</code>
     * @param \Google\Api\Distribution\Range $var
     * @return $this
     */
    public function setRange($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Distribution\Range::class);
        $this->range = $var;

        return $this;
    }

    /**
     * Defines the histogram bucket boundaries. If the distribution does not
     * contain a histogram, then omit this field.
     *
     * Generated from protobuf field <code>.google.api.Distribution.BucketOptions bucket_options = 6;</code>
     * @return \Google\Api\Distribution\BucketOptions|null
     */
    public function getBucketOptions()
    {
        return $this->bucket_options;
    }

    public function hasBucketOptions()
    {
        return isset($this->bucket_options);
    }

    public function clearBucketOptions()
    {
        unset($this->bucket_options);
    }

    /**
     * Defines the histogram bucket boundaries. If the distribution does not
     * contain a histogram, then omit this field.
     *
     * Generated from protobuf field <code>.google.api.Distribution.BucketOptions bucket_options = 6;</code>
     * @param \Google\Api\Distribution\BucketOptions $var
     * @return $this
     */
    public function setBucketOptions($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Distribution\BucketOptions::class);
        $this->bucket_options = $var;

        return $this;
    }

    /**
     * The number of values in each bucket of the histogram, as described in
     * `bucket_options`. If the distribution does not have a histogram, then omit
     * this field. If there is a histogram, then the sum of the values in
     * `bucket_counts` must equal the value in the `count` field of the
     * distribution.
     * If present, `bucket_counts` should contain N values, where N is the number
     * of buckets specified in `bucket_options`. If you supply fewer than N
     * values, the remaining values are assumed to be 0.
     * The order of the values in `bucket_counts` follows the bucket numbering
     * schemes described for the three bucket types. The first value must be the
     * count for the underflow bucket (number 0). The next N-2 values are the
     * counts for the finite buckets (number 1 through N-2). The N'th value in
     * `bucket_counts` is the count for the overflow bucket (number N-1).
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 7;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBucketCounts()
    {
        return $this->bucket_counts;
    }

    /**
     * The number of values in each bucket of the histogram, as described in
     * `bucket_options`. If the distribution does not have a histogram, then omit
     * this field. If there is a histogram, then the sum of the values in
     * `bucket_counts` must equal the value in the `count` field of the
     * distribution.
     * If present, `bucket_counts` should contain N values, where N is the number
     * of buckets specified in `bucket_options`. If you supply fewer than N
     * values, the remaining values are assumed to be 0.
     * The order of the values in `bucket_counts` follows the bucket numbering
     * schemes described for the three bucket types. The first value must be the
     * count for the underflow bucket (number 0). The next N-2 values are the
     * counts for the finite buckets (number 1 through N-2). The N'th value in
     * `bucket_counts` is the count for the overflow bucket (number N-1).
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 7;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBucketCounts($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT64);
        $this->bucket_counts = $arr;

        return $this;
    }

    /**
     * Must be in increasing order of `value` field.
     *
     * Generated from protobuf field <code>repeated .google.api.Distribution.Exemplar exemplars = 10;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExemplars()
    {
        return $this->exemplars;
    }

    /**
     * Must be in increasing order of `value` field.
     *
     * Generated from protobuf field <code>repeated .google.api.Distribution.Exemplar exemplars = 10;</code>
     * @param array<\Google\Api\Distribution\Exemplar>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExemplars($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Api\Distribution\Exemplar::class);
        $this->exemplars = $arr;

        return $this;
    }

}

