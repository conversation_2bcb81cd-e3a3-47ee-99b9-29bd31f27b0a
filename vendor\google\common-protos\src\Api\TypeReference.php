<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/field_info.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A reference to a message type, for use in [FieldInfo][google.api.FieldInfo].
 *
 * Generated from protobuf message <code>google.api.TypeReference</code>
 */
class TypeReference extends \Google\Protobuf\Internal\Message
{
    /**
     * The name of the type that the annotated, generic field may represent.
     * If the type is in the same protobuf package, the value can be the simple
     * message name e.g., `"MyMessage"`. Otherwise, the value must be the
     * fully-qualified message name e.g., `"google.library.v1.Book"`.
     * If the type(s) are unknown to the service (e.g. the field accepts generic
     * user input), use the wildcard `"*"` to denote this behavior.
     * See [AIP-202](https://google.aip.dev/202#type-references) for more details.
     *
     * Generated from protobuf field <code>string type_name = 1;</code>
     */
    protected $type_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $type_name
     *           The name of the type that the annotated, generic field may represent.
     *           If the type is in the same protobuf package, the value can be the simple
     *           message name e.g., `"MyMessage"`. Otherwise, the value must be the
     *           fully-qualified message name e.g., `"google.library.v1.Book"`.
     *           If the type(s) are unknown to the service (e.g. the field accepts generic
     *           user input), use the wildcard `"*"` to denote this behavior.
     *           See [AIP-202](https://google.aip.dev/202#type-references) for more details.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\FieldInfo::initOnce();
        parent::__construct($data);
    }

    /**
     * The name of the type that the annotated, generic field may represent.
     * If the type is in the same protobuf package, the value can be the simple
     * message name e.g., `"MyMessage"`. Otherwise, the value must be the
     * fully-qualified message name e.g., `"google.library.v1.Book"`.
     * If the type(s) are unknown to the service (e.g. the field accepts generic
     * user input), use the wildcard `"*"` to denote this behavior.
     * See [AIP-202](https://google.aip.dev/202#type-references) for more details.
     *
     * Generated from protobuf field <code>string type_name = 1;</code>
     * @return string
     */
    public function getTypeName()
    {
        return $this->type_name;
    }

    /**
     * The name of the type that the annotated, generic field may represent.
     * If the type is in the same protobuf package, the value can be the simple
     * message name e.g., `"MyMessage"`. Otherwise, the value must be the
     * fully-qualified message name e.g., `"google.library.v1.Book"`.
     * If the type(s) are unknown to the service (e.g. the field accepts generic
     * user input), use the wildcard `"*"` to denote this behavior.
     * See [AIP-202](https://google.aip.dev/202#type-references) for more details.
     *
     * Generated from protobuf field <code>string type_name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTypeName($var)
    {
        GPBUtil::checkString($var, True);
        $this->type_name = $var;

        return $this;
    }

}

