<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

?>
<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <!-- Subscribe Button Demo -->
    <div class="q-demo-section">
        <h2><?php esc_html_e('Subscribe Button Demo', 'formidable-firebase-push'); ?></h2>
        <p><?php esc_html_e('Use this shortcode to add a subscribe button to any page or post:', 'formidable-firebase-push'); ?>
        </p>
        <code class="q-shortcode-display">[q_subscribe_button]</code>
        <button class="button button-secondary" id="q-copy-shortcode">
            <i class="bi bi-clipboard"></i> <?php esc_html_e('Copy Shortcode', 'formidable-firebase-push'); ?>
        </button>
        <div class="q-demo-preview">
            <h3><?php esc_html_e('Preview:', 'formidable-firebase-push'); ?></h3>
            <?php echo do_shortcode('[q_subscribe_button]'); ?>
        </div>
    </div>

    <!-- Overall Analytics Dashboard -->
    <div class="q-analytics-dashboard">
        <div class="q-analytics-card">
            <h3><?php esc_html_e('Total Subscribers', 'formidable-firebase-push'); ?></h3>
            <div class="q-analytics-number"><?php echo is_array($subscribers) ? count($subscribers) : 0; ?></div>
        </div>
        <div class="q-analytics-card">
            <h3><?php esc_html_e('Total Notifications', 'formidable-firebase-push'); ?></h3>
            <div class="q-analytics-number"><?php echo esc_html($total_notifications); ?></div>
        </div>
        <div class="q-analytics-card">
            <h3><?php esc_html_e('Overall Engagement Rate', 'formidable-firebase-push'); ?></h3>
            <div class="q-analytics-number"><?php echo esc_html($engagement_rate); ?>%</div>
        </div>
    </div>

    <!-- Admin Controls -->
    <div class="q-analytics-controls">
        <button id="q-check-notifications" class="button button-primary">
            <i class="bi bi-bell"></i>
            <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>
        </button>
        <button id="q-clear-analytics" class="button button-secondary">
            <i class="bi bi-trash"></i> <?php esc_html_e('Clear Analytics Data', 'formidable-firebase-push'); ?>
        </button>
    </div>

    <!-- Per-Form Analytics -->
    <div class="q-form-analytics">
        <h2><?php esc_html_e('Form-specific Analytics', 'formidable-firebase-push'); ?></h2>

        <?php
        $form_analytics = q_get_form_analytics();
        $actions = FrmFormAction::get_action_for_form(null, 'firebase_push');

        if (!empty($actions)): ?>
            <div class="q-form-analytics-grid">
                <?php foreach ($actions as $action):
                    $form = FrmForm::getOne($action->menu_order);
                    if (!$form)
                        continue;

                    $form_id = $action->menu_order;
                    $analytics = isset($form_analytics[$form_id]) ? $form_analytics[$form_id] : array(
                        'total_notifications' => 0,
                        'engagement_rate' => 0
                    );
                    ?>
                    <div class="q-form-analytics-card">
                        <h3><?php echo esc_html($form->name); ?></h3>
                        <div class="q-form-analytics-stats">
                            <div class="q-stat">
                                <label><?php esc_html_e('Notifications Sent', 'formidable-firebase-push'); ?></label>
                                <span><?php echo esc_html($analytics['total_notifications']); ?></span>
                            </div>
                            <div class="q-stat">
                                <label><?php esc_html_e('Engagement Rate', 'formidable-firebase-push'); ?></label>
                                <span><?php echo esc_html($analytics['engagement_rate']); ?>%</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p><?php esc_html_e('No form actions configured yet.', 'formidable-firebase-push'); ?></p>
        <?php endif; ?>
    </div>

    <!-- Subscriber Management -->
    <div class="q-subscriber-controls">
        <div class="q-search-box">
            <input type="text" id="q-subscriber-search"
                placeholder="<?php esc_attr_e('Search subscribers...', 'formidable-firebase-push'); ?>">
        </div>
        <div class="q-bulk-actions">
            <select id="q-bulk-action">
                <option value=""><?php esc_html_e('Bulk Actions', 'formidable-firebase-push'); ?></option>
                <option value="remove"><?php esc_html_e('Remove Selected', 'formidable-firebase-push'); ?></option>
                <option value="test"><?php esc_html_e('Send Test Notification', 'formidable-firebase-push'); ?></option>
            </select>
            <button class="button" id="q-bulk-apply"><?php esc_html_e('Apply', 'formidable-firebase-push'); ?></button>
        </div>
    </div>

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th class="check-column"><input type="checkbox" id="q-select-all"></th>
                <th scope="col"><?php esc_html_e('User', 'formidable-firebase-push'); ?></th>
                <th scope="col"><?php esc_html_e('Email', 'formidable-firebase-push'); ?></th>
                <th scope="col"><?php esc_html_e('Devices', 'formidable-firebase-push'); ?></th>
                <th scope="col"><?php esc_html_e('Subscribed Date', 'formidable-firebase-push'); ?></th>
                <th scope="col"><?php esc_html_e('Last Active', 'formidable-firebase-push'); ?></th>
                <th scope="col"><?php esc_html_e('Actions', 'formidable-firebase-push'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($subscribers as $subscriber):
                $last_active = get_user_meta($subscriber->ID, 'q_last_notification_interaction', true);
                $device_count = q_get_user_device_count($subscriber->ID);
                ?>
                <tr>
                    <td><input type="checkbox" class="q-subscriber-select" value="<?php echo esc_attr($subscriber->ID); ?>">
                    </td>
                    <td><?php echo esc_html($subscriber->display_name); ?></td>
                    <td><?php echo esc_html($subscriber->user_email); ?></td>
                    <td>
                        <span class="q-device-count"
                            title="<?php esc_attr_e('Number of registered devices', 'formidable-firebase-push'); ?>">
                            <i class="bi bi-phone"></i> <?php echo esc_html($device_count); ?>
                        </span>
                    </td>
                    <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($subscriber->user_registered))); ?>
                    </td>
                    <td><?php echo $last_active ? esc_html(human_time_diff(strtotime($last_active))) : '-'; ?></td>
                    <td class="q-action-buttons">
                        <button class="button button-primary button-small send-test"
                            data-user-id="<?php echo esc_attr($subscriber->ID); ?>"
                            data-nonce="<?php echo esc_attr(wp_create_nonce('test_notification_' . $subscriber->ID)); ?>">
                            <i class="bi bi-bell"></i> <?php esc_html_e('Test', 'formidable-firebase-push'); ?>
                        </button>
                        <button class="button button-small remove-subscriber"
                            data-user-id="<?php echo esc_attr($subscriber->ID); ?>"
                            data-nonce="<?php echo esc_attr(wp_create_nonce('remove_subscriber_' . $subscriber->ID)); ?>">
                            <i class="bi bi-trash"></i> <?php esc_html_e('Remove', 'formidable-firebase-push'); ?>
                        </button>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<style>
    .q-analytics-dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .q-analytics-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .q-analytics-number {
        font-size: 24px;
        font-weight: bold;
        color: #0384c6;
    }

    .q-subscriber-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
        gap: 20px;
    }

    .q-search-box {
        flex: 1;
    }

    #q-subscriber-search {
        width: 100%;
        padding: 8px;
    }

    .q-action-buttons {
        display: flex;
        gap: 5px;
    }

    .q-action-buttons button {
        padding: 4px 8px;
    }

    .bi {
        font-size: 14px;
    }

    .q-form-analytics {
        margin-top: 2em;
    }

    .q-form-analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1em;
        margin-top: 1em;
    }

    .q-form-analytics-card {
        background: white;
        padding: 1.5em;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .q-form-analytics-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1em;
        margin-top: 1em;
    }

    .q-stat {
        text-align: center;
    }

    .q-stat label {
        display: block;
        font-size: 0.9em;
        color: #666;
        margin-bottom: 0.5em;
    }

    .q-stat span {
        font-size: 1.2em;
        font-weight: bold;
        color: #333;
    }

    .q-demo-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }

    .q-shortcode-display {
        background: #f5f5f5;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        margin-right: 10px;
    }

    .q-demo-preview {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    #q-copy-shortcode {
        vertical-align: middle;
    }

    #q-copy-shortcode i {
        margin-right: 5px;
    }

    .q-analytics-controls {
        text-align: right;
        margin: -10px 0 20px 0;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    #q-clear-analytics,
    #q-check-notifications {
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    #q-clear-analytics .bi,
    #q-check-notifications .bi {
        font-size: 14px;
    }

    .q-device-count {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 2px 8px;
        background: #f0f0f1;
        border-radius: 12px;
        font-size: 13px;
    }

    .q-device-count .bi {
        font-size: 14px;
        color: #2271b1;
    }
</style>

<script type="text/javascript">
    jQuery(document).ready(function ($) {
        // Search functionality
        $('#q-subscriber-search').on('input', function () {
            const searchTerm = $(this).val().toLowerCase();
            $('tbody tr').each(function () {
                const text = $(this).text().toLowerCase();
                $(this).toggle(text.includes(searchTerm));
            });
        });

        // Bulk selection
        $('#q-select-all').on('change', function () {
            $('.q-subscriber-select').prop('checked', $(this).prop('checked'));
        });

        // Bulk actions
        $('#q-bulk-apply').on('click', function () {
            const action = $('#q-bulk-action').val();
            const selected = $('.q-subscriber-select:checked').map(function () {
                return $(this).val();
            }).get();

            if (!selected.length) {
                alert('<?php esc_html_e('Please select subscribers first.', 'formidable-firebase-push'); ?>');
                return;
            }

            switch (action) {
                case 'remove':
                    if (confirm('<?php esc_html_e('Are you sure you want to remove the selected subscribers?', 'formidable-firebase-push'); ?>')) {
                        removeSubscribers(selected);
                    }
                    break;
                case 'test':
                    sendTestNotifications(selected);
                    break;
            }
        });

        // Send test notification
        $('.send-test').on('click', function (e) {
            e.preventDefault();
            const button = $(this);
            const userId = button.data('user-id');
            const nonce = button.data('nonce');

            button.prop('disabled', true);

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'q_send_test_notification',
                    user_id: userId,
                    nonce: nonce
                },
                success: function (response) {
                    if (response.success) {
                        alert(response.data);
                    } else {
                        alert(response.data);
                    }
                    button.prop('disabled', false);
                },
                error: function () {
                    alert('<?php esc_html_e('An error occurred. Please try again.', 'formidable-firebase-push'); ?>');
                    button.prop('disabled', false);
                }
            });
        });

        // Remove subscriber
        $('.remove-subscriber').on('click', function (e) {
            e.preventDefault();
            const button = $(this);
            const userId = button.data('user-id');
            const nonce = button.data('nonce');

            if (confirm('<?php esc_html_e('Are you sure you want to remove this subscription?', 'formidable-firebase-push'); ?>')) {
                button.prop('disabled', true);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'q_remove_subscriber',
                        user_id: userId,
                        nonce: nonce
                    },
                    success: function (response) {
                        if (response.success) {
                            button.closest('tr').fadeOut(400, function () {
                                $(this).remove();
                                if ($('tbody tr').length === 0) {
                                    $('tbody').append('<tr><td colspan="6"><?php esc_html_e('No subscribers found.', 'formidable-firebase-push'); ?></td></tr>');
                                }
                                // Trigger a custom event that the subscription.js can listen for
                                window.dispatchEvent(new CustomEvent('subscriptionRemoved', {
                                    detail: { userId: userId }
                                }));
                            });
                        } else {
                            alert(response.data);
                            button.prop('disabled', false);
                        }
                    },
                    error: function () {
                        alert('<?php esc_html_e('An error occurred. Please try again.', 'formidable-firebase-push'); ?>');
                        button.prop('disabled', false);
                    }
                });
            }
        });

        // Manual notification check functionality
        $('#q-check-notifications').on('click', function () {
            const button = $(this);
            button.prop('disabled', true);
            button.html('<i class="bi bi-hourglass-split"></i> <?php esc_html_e('Checking...', 'formidable-firebase-push'); ?>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'q_manual_check_notifications',
                    nonce: '<?php echo wp_create_nonce('q_manual_check_notifications'); ?>'
                },
                success: function (response) {
                    if (response.success) {
                        alert(response.data.message);
                    } else {
                        alert(response.data || '<?php esc_html_e('An error occurred while checking notifications.', 'formidable-firebase-push'); ?>');
                    }
                    button.html('<i class="bi bi-bell"></i> <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>');
                    button.prop('disabled', false);
                },
                error: function () {
                    alert('<?php esc_html_e('An error occurred while checking notifications.', 'formidable-firebase-push'); ?>');
                    button.html('<i class="bi bi-bell"></i> <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>');
                    button.prop('disabled', false);
                }
            });
        });

        // Clear Analytics functionality
        $('#q-clear-analytics').on('click', function () {
            if (!confirm('<?php esc_html_e('Are you sure you want to clear all analytics data? This action cannot be undone.', 'formidable-firebase-push'); ?>')) {
                return;
            }

            const button = $(this);
            button.prop('disabled', true);

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'q_clear_analytics',
                    nonce: '<?php echo wp_create_nonce('q_clear_analytics'); ?>'
                },
                success: function (response) {
                    if (response.success) {
                        // Update analytics numbers to zero
                        $('.q-analytics-number').text('0');
                        $('.q-stat span').text('0');
                        alert(response.data);
                    } else {
                        alert(response.data);
                    }
                    button.prop('disabled', false);
                },
                error: function () {
                    alert('<?php esc_html_e('An error occurred while clearing analytics data.', 'formidable-firebase-push'); ?>');
                    button.prop('disabled', false);
                }
            });
        });
    });
</script>