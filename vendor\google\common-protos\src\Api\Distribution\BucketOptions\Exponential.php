<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api\Distribution\BucketOptions;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Specifies an exponential sequence of buckets that have a width that is
 * proportional to the value of the lower bound. Each bucket represents a
 * constant relative uncertainty on a specific value in the bucket.
 * There are `num_finite_buckets + 2` (= N) buckets. Bucket `i` has the
 * following boundaries:
 *    Upper bound (0 <= i < N-1):     scale * (growth_factor ^ i).
 *    Lower bound (1 <= i < N):       scale * (growth_factor ^ (i - 1)).
 *
 * Generated from protobuf message <code>google.api.Distribution.BucketOptions.Exponential</code>
 */
class Exponential extends \Google\Protobuf\Internal\Message
{
    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     */
    protected $num_finite_buckets = 0;
    /**
     * Must be greater than 1.
     *
     * Generated from protobuf field <code>double growth_factor = 2;</code>
     */
    protected $growth_factor = 0.0;
    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>double scale = 3;</code>
     */
    protected $scale = 0.0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $num_finite_buckets
     *           Must be greater than 0.
     *     @type float $growth_factor
     *           Must be greater than 1.
     *     @type float $scale
     *           Must be greater than 0.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Distribution::initOnce();
        parent::__construct($data);
    }

    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     * @return int
     */
    public function getNumFiniteBuckets()
    {
        return $this->num_finite_buckets;
    }

    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setNumFiniteBuckets($var)
    {
        GPBUtil::checkInt32($var);
        $this->num_finite_buckets = $var;

        return $this;
    }

    /**
     * Must be greater than 1.
     *
     * Generated from protobuf field <code>double growth_factor = 2;</code>
     * @return float
     */
    public function getGrowthFactor()
    {
        return $this->growth_factor;
    }

    /**
     * Must be greater than 1.
     *
     * Generated from protobuf field <code>double growth_factor = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setGrowthFactor($var)
    {
        GPBUtil::checkDouble($var);
        $this->growth_factor = $var;

        return $this;
    }

    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>double scale = 3;</code>
     * @return float
     */
    public function getScale()
    {
        return $this->scale;
    }

    /**
     * Must be greater than 0.
     *
     * Generated from protobuf field <code>double scale = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setScale($var)
    {
        GPBUtil::checkDouble($var);
        $this->scale = $var;

        return $this;
    }

}


