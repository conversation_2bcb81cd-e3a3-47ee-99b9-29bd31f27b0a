<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/policy.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Defines policies applying to an RPC method.
 *
 * Generated from protobuf message <code>google.api.MethodPolicy</code>
 */
class MethodPolicy extends \Google\Protobuf\Internal\Message
{
    /**
     * Selects a method to which these policies should be enforced, for example,
     * "google.pubsub.v1.Subscriber.CreateSubscription".
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax
     * details.
     * NOTE: This field must not be set in the proto annotation. It will be
     * automatically filled by the service config compiler .
     *
     * Generated from protobuf field <code>string selector = 9;</code>
     */
    protected $selector = '';
    /**
     * Policies that are applicable to the request message.
     *
     * Generated from protobuf field <code>repeated .google.api.FieldPolicy request_policies = 2;</code>
     */
    private $request_policies;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $selector
     *           Selects a method to which these policies should be enforced, for example,
     *           "google.pubsub.v1.Subscriber.CreateSubscription".
     *           Refer to [selector][google.api.DocumentationRule.selector] for syntax
     *           details.
     *           NOTE: This field must not be set in the proto annotation. It will be
     *           automatically filled by the service config compiler .
     *     @type array<\Google\Api\FieldPolicy>|\Google\Protobuf\Internal\RepeatedField $request_policies
     *           Policies that are applicable to the request message.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Policy::initOnce();
        parent::__construct($data);
    }

    /**
     * Selects a method to which these policies should be enforced, for example,
     * "google.pubsub.v1.Subscriber.CreateSubscription".
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax
     * details.
     * NOTE: This field must not be set in the proto annotation. It will be
     * automatically filled by the service config compiler .
     *
     * Generated from protobuf field <code>string selector = 9;</code>
     * @return string
     */
    public function getSelector()
    {
        return $this->selector;
    }

    /**
     * Selects a method to which these policies should be enforced, for example,
     * "google.pubsub.v1.Subscriber.CreateSubscription".
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax
     * details.
     * NOTE: This field must not be set in the proto annotation. It will be
     * automatically filled by the service config compiler .
     *
     * Generated from protobuf field <code>string selector = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setSelector($var)
    {
        GPBUtil::checkString($var, True);
        $this->selector = $var;

        return $this;
    }

    /**
     * Policies that are applicable to the request message.
     *
     * Generated from protobuf field <code>repeated .google.api.FieldPolicy request_policies = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getRequestPolicies()
    {
        return $this->request_policies;
    }

    /**
     * Policies that are applicable to the request message.
     *
     * Generated from protobuf field <code>repeated .google.api.FieldPolicy request_policies = 2;</code>
     * @param array<\Google\Api\FieldPolicy>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRequestPolicies($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Api\FieldPolicy::class);
        $this->request_policies = $arr;

        return $this;
    }

}

