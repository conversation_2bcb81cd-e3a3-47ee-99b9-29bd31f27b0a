name: Tests

on:
  pull_request:
    paths:
      - 'src/**'
      - 'tests/**'
      - '.github/workflows/tests.yml'
      - 'composer.json'
  push:
    branches: ['main']
    paths:
      - 'src/**'
      - 'tests/**'
      - '.github/workflows/tests.yml'
      - 'composer.json'
  workflow_dispatch:

jobs:
  tests:
    name: PHP ${{ matrix.php }}
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php:
          - "8.1"
          - "8.2"
          - "8.3"
          - "8.4"

    env:
      extensions: json
      key: "cache-${{ matrix.php }}-${{ matrix.dependencies }}-v2" # can be any string, change to clear the extension cache.

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup cache environment
        id: extcache
        uses: shivammathur/cache-extensions@v1
        with:
          php-version: ${{ matrix.php }}
          extensions: ${{ env.extensions }}
          key: ${{ env.key }}

      - name: Cache extensions
        uses: actions/cache@v4
        with:
          path: ${{ steps.extcache.outputs.dir }}
          key: ${{ steps.extcache.outputs.key }}
          restore-keys: ${{ steps.extcache.outputs.key }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: ${{ env.extensions }}
          tools: composer, pecl
          coverage: xdebug
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: "ramsey/composer-install@v3"

      - name: Setup Problem Matchers
        run: |
          echo "::add-matcher::${{ runner.tool_cache }}/php.json"
          echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Run PHPStan
        run: vendor/bin/phpstan

      - name: Run PHPUnit
        run: vendor/bin/phpunit --testdox --coverage-text
