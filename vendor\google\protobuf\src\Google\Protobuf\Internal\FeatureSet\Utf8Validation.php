<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FeatureSet;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FeatureSet.Utf8Validation</code>
 */
class Utf8Validation
{
    /**
     * Generated from protobuf enum <code>UTF8_VALIDATION_UNKNOWN = 0;</code>
     */
    const UTF8_VALIDATION_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>VERIFY = 2;</code>
     */
    const VERIFY = 2;
    /**
     * Generated from protobuf enum <code>NONE = 3;</code>
     */
    const NONE = 3;

    private static $valueToName = [
        self::UTF8_VALIDATION_UNKNOWN => 'UTF8_VALIDATION_UNKNOWN',
        self::VERIFY => 'VERIFY',
        self::NONE => 'NONE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

