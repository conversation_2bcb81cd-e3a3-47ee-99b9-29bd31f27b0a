{"name": "beste/clock", "type": "library", "description": "A collection of Clock implementations", "keywords": ["clock", "clock-interface", "psr20", "psr-20"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "psr/clock": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9.1", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.29"}, "autoload": {"psr-4": {"Beste\\Clock\\": "src/Clock"}, "files": ["src/Clock.php"]}, "autoload-dev": {"psr-4": {"Beste\\Clock\\Tests\\": "tests/Clock"}}, "provide": {"psr/clock-implementation": "1.0"}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "scripts": {"test": ["@phpstan", "@psalm", "@phpunit"], "phpunit": "vendor/bin/phpunit", "phpstan": "vendor/bin/phpstan analyse", "psalm": "vendor/bin/psalm"}}