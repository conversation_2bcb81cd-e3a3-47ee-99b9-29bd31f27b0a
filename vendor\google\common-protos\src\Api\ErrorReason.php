<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/error_reason.proto

namespace Google\Api;

use UnexpectedValueException;

/**
 * Defines the supported values for `google.rpc.ErrorInfo.reason` for the
 * `googleapis.com` error domain. This error domain is reserved for [Service
 * Infrastructure](https://cloud.google.com/service-infrastructure/docs/overview).
 * For each error info of this domain, the metadata key "service" refers to the
 * logical identifier of an API service, such as "pubsub.googleapis.com". The
 * "consumer" refers to the entity that consumes an API Service. It typically is
 * a Google project that owns the client application or the server resource,
 * such as "projects/123". Other metadata keys are specific to each error
 * reason. For more information, see the definition of the specific error
 * reason.
 *
 * Protobuf type <code>google.api.ErrorReason</code>
 */
class ErrorReason
{
    /**
     * Do not use this default value.
     *
     * Generated from protobuf enum <code>ERROR_REASON_UNSPECIFIED = 0;</code>
     */
    const ERROR_REASON_UNSPECIFIED = 0;
    /**
     * The request is calling a disabled service for a consumer.
     * Example of an ErrorInfo when the consumer "projects/123" contacting
     * "pubsub.googleapis.com" service which is disabled:
     *     { "reason": "SERVICE_DISABLED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "pubsub.googleapis.com"
     *       }
     *     }
     * This response indicates the "pubsub.googleapis.com" has been disabled in
     * "projects/123".
     *
     * Generated from protobuf enum <code>SERVICE_DISABLED = 1;</code>
     */
    const SERVICE_DISABLED = 1;
    /**
     * The request whose associated billing account is disabled.
     * Example of an ErrorInfo when the consumer "projects/123" fails to contact
     * "pubsub.googleapis.com" service because the associated billing account is
     * disabled:
     *     { "reason": "BILLING_DISABLED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "pubsub.googleapis.com"
     *       }
     *     }
     * This response indicates the billing account associated has been disabled.
     *
     * Generated from protobuf enum <code>BILLING_DISABLED = 2;</code>
     */
    const BILLING_DISABLED = 2;
    /**
     * The request is denied because the provided [API
     * key](https://cloud.google.com/docs/authentication/api-keys) is invalid. It
     * may be in a bad format, cannot be found, or has been expired).
     * Example of an ErrorInfo when the request is contacting
     * "storage.googleapis.com" service with an invalid API key:
     *     { "reason": "API_KEY_INVALID",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_INVALID = 3;</code>
     */
    const API_KEY_INVALID = 3;
    /**
     * The request is denied because it violates [API key API
     * restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_api_restrictions).
     * Example of an ErrorInfo when the consumer "projects/123" fails to call the
     * "storage.googleapis.com" service because this service is restricted in the
     * API key:
     *     { "reason": "API_KEY_SERVICE_BLOCKED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_SERVICE_BLOCKED = 4;</code>
     */
    const API_KEY_SERVICE_BLOCKED = 4;
    /**
     * The request is denied because it violates [API key HTTP
     * restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_http_restrictions).
     * Example of an ErrorInfo when the consumer "projects/123" fails to call
     * "storage.googleapis.com" service because the http referrer of the request
     * violates API key HTTP restrictions:
     *     { "reason": "API_KEY_HTTP_REFERRER_BLOCKED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com",
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_HTTP_REFERRER_BLOCKED = 7;</code>
     */
    const API_KEY_HTTP_REFERRER_BLOCKED = 7;
    /**
     * The request is denied because it violates [API key IP address
     * restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
     * Example of an ErrorInfo when the consumer "projects/123" fails to call
     * "storage.googleapis.com" service because the caller IP of the request
     * violates API key IP address restrictions:
     *     { "reason": "API_KEY_IP_ADDRESS_BLOCKED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com",
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_IP_ADDRESS_BLOCKED = 8;</code>
     */
    const API_KEY_IP_ADDRESS_BLOCKED = 8;
    /**
     * The request is denied because it violates [API key Android application
     * restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
     * Example of an ErrorInfo when the consumer "projects/123" fails to call
     * "storage.googleapis.com" service because the request from the Android apps
     * violates the API key Android application restrictions:
     *     { "reason": "API_KEY_ANDROID_APP_BLOCKED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_ANDROID_APP_BLOCKED = 9;</code>
     */
    const API_KEY_ANDROID_APP_BLOCKED = 9;
    /**
     * The request is denied because it violates [API key iOS application
     * restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_application_restrictions).
     * Example of an ErrorInfo when the consumer "projects/123" fails to call
     * "storage.googleapis.com" service because the request from the iOS apps
     * violates the API key iOS application restrictions:
     *     { "reason": "API_KEY_IOS_APP_BLOCKED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>API_KEY_IOS_APP_BLOCKED = 13;</code>
     */
    const API_KEY_IOS_APP_BLOCKED = 13;
    /**
     * The request is denied because there is not enough rate quota for the
     * consumer.
     * Example of an ErrorInfo when the consumer "projects/123" fails to contact
     * "pubsub.googleapis.com" service because consumer's rate quota usage has
     * reached the maximum value set for the quota limit
     * "ReadsPerMinutePerProject" on the quota metric
     * "pubsub.googleapis.com/read_requests":
     *     { "reason": "RATE_LIMIT_EXCEEDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "pubsub.googleapis.com",
     *         "quota_metric": "pubsub.googleapis.com/read_requests",
     *         "quota_limit": "ReadsPerMinutePerProject"
     *       }
     *     }
     * Example of an ErrorInfo when the consumer "projects/123" checks quota on
     * the service "dataflow.googleapis.com" and hits the organization quota
     * limit "DefaultRequestsPerMinutePerOrganization" on the metric
     * "dataflow.googleapis.com/default_requests".
     *     { "reason": "RATE_LIMIT_EXCEEDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "dataflow.googleapis.com",
     *         "quota_metric": "dataflow.googleapis.com/default_requests",
     *         "quota_limit": "DefaultRequestsPerMinutePerOrganization"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>RATE_LIMIT_EXCEEDED = 5;</code>
     */
    const RATE_LIMIT_EXCEEDED = 5;
    /**
     * The request is denied because there is not enough resource quota for the
     * consumer.
     * Example of an ErrorInfo when the consumer "projects/123" fails to contact
     * "compute.googleapis.com" service because consumer's resource quota usage
     * has reached the maximum value set for the quota limit "VMsPerProject"
     * on the quota metric "compute.googleapis.com/vms":
     *     { "reason": "RESOURCE_QUOTA_EXCEEDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "compute.googleapis.com",
     *         "quota_metric": "compute.googleapis.com/vms",
     *         "quota_limit": "VMsPerProject"
     *       }
     *     }
     * Example of an ErrorInfo when the consumer "projects/123" checks resource
     * quota on the service "dataflow.googleapis.com" and hits the organization
     * quota limit "jobs-per-organization" on the metric
     * "dataflow.googleapis.com/job_count".
     *     { "reason": "RESOURCE_QUOTA_EXCEEDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "dataflow.googleapis.com",
     *         "quota_metric": "dataflow.googleapis.com/job_count",
     *         "quota_limit": "jobs-per-organization"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>RESOURCE_QUOTA_EXCEEDED = 6;</code>
     */
    const RESOURCE_QUOTA_EXCEEDED = 6;
    /**
     * The request whose associated billing account address is in a tax restricted
     * location, violates the local tax restrictions when creating resources in
     * the restricted region.
     * Example of an ErrorInfo when creating the Cloud Storage Bucket in the
     * container "projects/123" under a tax restricted region
     * "locations/asia-northeast3":
     *     { "reason": "LOCATION_TAX_POLICY_VIOLATED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com",
     *         "location": "locations/asia-northeast3"
     *       }
     *     }
     * This response indicates creating the Cloud Storage Bucket in
     * "locations/asia-northeast3" violates the location tax restriction.
     *
     * Generated from protobuf enum <code>LOCATION_TAX_POLICY_VIOLATED = 10;</code>
     */
    const LOCATION_TAX_POLICY_VIOLATED = 10;
    /**
     * The request is denied because the caller does not have required permission
     * on the user project "projects/123" or the user project is invalid. For more
     * information, check the [userProject System
     * Parameters](https://cloud.google.com/apis/docs/system-parameters).
     * Example of an ErrorInfo when the caller is calling Cloud Storage service
     * with insufficient permissions on the user project:
     *     { "reason": "USER_PROJECT_DENIED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>USER_PROJECT_DENIED = 11;</code>
     */
    const USER_PROJECT_DENIED = 11;
    /**
     * The request is denied because the consumer "projects/123" is suspended due
     * to Terms of Service(Tos) violations. Check [Project suspension
     * guidelines](https://cloud.google.com/resource-manager/docs/project-suspension-guidelines)
     * for more information.
     * Example of an ErrorInfo when calling Cloud Storage service with the
     * suspended consumer "projects/123":
     *     { "reason": "CONSUMER_SUSPENDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>CONSUMER_SUSPENDED = 12;</code>
     */
    const CONSUMER_SUSPENDED = 12;
    /**
     * The request is denied because the associated consumer is invalid. It may be
     * in a bad format, cannot be found, or have been deleted.
     * Example of an ErrorInfo when calling Cloud Storage service with the
     * invalid consumer "projects/123":
     *     { "reason": "CONSUMER_INVALID",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>CONSUMER_INVALID = 14;</code>
     */
    const CONSUMER_INVALID = 14;
    /**
     * The request is denied because it violates [VPC Service
     * Controls](https://cloud.google.com/vpc-service-controls/docs/overview).
     * The 'uid' field is a random generated identifier that customer can use it
     * to search the audit log for a request rejected by VPC Service Controls. For
     * more information, please refer [VPC Service Controls
     * Troubleshooting](https://cloud.google.com/vpc-service-controls/docs/troubleshooting#unique-id)
     * Example of an ErrorInfo when the consumer "projects/123" fails to call
     * Cloud Storage service because the request is prohibited by the VPC Service
     * Controls.
     *     { "reason": "SECURITY_POLICY_VIOLATED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "uid": "123456789abcde",
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>SECURITY_POLICY_VIOLATED = 15;</code>
     */
    const SECURITY_POLICY_VIOLATED = 15;
    /**
     * The request is denied because the provided access token has expired.
     * Example of an ErrorInfo when the request is calling Cloud Storage service
     * with an expired access token:
     *     { "reason": "ACCESS_TOKEN_EXPIRED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>ACCESS_TOKEN_EXPIRED = 16;</code>
     */
    const ACCESS_TOKEN_EXPIRED = 16;
    /**
     * The request is denied because the provided access token doesn't have at
     * least one of the acceptable scopes required for the API. Please check
     * [OAuth 2.0 Scopes for Google
     * APIs](https://developers.google.com/identity/protocols/oauth2/scopes) for
     * the list of the OAuth 2.0 scopes that you might need to request to access
     * the API.
     * Example of an ErrorInfo when the request is calling Cloud Storage service
     * with an access token that is missing required scopes:
     *     { "reason": "ACCESS_TOKEN_SCOPE_INSUFFICIENT",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>ACCESS_TOKEN_SCOPE_INSUFFICIENT = 17;</code>
     */
    const ACCESS_TOKEN_SCOPE_INSUFFICIENT = 17;
    /**
     * The request is denied because the account associated with the provided
     * access token is in an invalid state, such as disabled or deleted.
     * For more information, see https://cloud.google.com/docs/authentication.
     * Warning: For privacy reasons, the server may not be able to disclose the
     * email address for some accounts. The client MUST NOT depend on the
     * availability of the `email` attribute.
     * Example of an ErrorInfo when the request is to the Cloud Storage API with
     * an access token that is associated with a disabled or deleted [service
     * account](http://cloud/iam/docs/service-accounts):
     *     { "reason": "ACCOUNT_STATE_INVALID",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject",
     *         "email": "user&#64;123.iam.gserviceaccount.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>ACCOUNT_STATE_INVALID = 18;</code>
     */
    const ACCOUNT_STATE_INVALID = 18;
    /**
     * The request is denied because the type of the provided access token is not
     * supported by the API being called.
     * Example of an ErrorInfo when the request is to the Cloud Storage API with
     * an unsupported token type.
     *     { "reason": "ACCESS_TOKEN_TYPE_UNSUPPORTED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>ACCESS_TOKEN_TYPE_UNSUPPORTED = 19;</code>
     */
    const ACCESS_TOKEN_TYPE_UNSUPPORTED = 19;
    /**
     * The request is denied because the request doesn't have any authentication
     * credentials. For more information regarding the supported authentication
     * strategies for Google Cloud APIs, see
     * https://cloud.google.com/docs/authentication.
     * Example of an ErrorInfo when the request is to the Cloud Storage API
     * without any authentication credentials.
     *     { "reason": "CREDENTIALS_MISSING",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>CREDENTIALS_MISSING = 20;</code>
     */
    const CREDENTIALS_MISSING = 20;
    /**
     * The request is denied because the provided project owning the resource
     * which acts as the [API
     * consumer](https://cloud.google.com/apis/design/glossary#api_consumer) is
     * invalid. It may be in a bad format or empty.
     * Example of an ErrorInfo when the request is to the Cloud Functions API,
     * but the offered resource project in the request in a bad format which can't
     * perform the ListFunctions method.
     *     { "reason": "RESOURCE_PROJECT_INVALID",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "cloudfunctions.googleapis.com",
     *         "method":
     *         "google.cloud.functions.v1.CloudFunctionsService.ListFunctions"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>RESOURCE_PROJECT_INVALID = 21;</code>
     */
    const RESOURCE_PROJECT_INVALID = 21;
    /**
     * The request is denied because the provided session cookie is missing,
     * invalid or failed to decode.
     * Example of an ErrorInfo when the request is calling Cloud Storage service
     * with a SID cookie which can't be decoded.
     *     { "reason": "SESSION_COOKIE_INVALID",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject",
     *         "cookie": "SID"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>SESSION_COOKIE_INVALID = 23;</code>
     */
    const SESSION_COOKIE_INVALID = 23;
    /**
     * The request is denied because the user is from a Google Workspace customer
     * that blocks their users from accessing a particular service.
     * Example scenario: https://support.google.com/a/answer/9197205?hl=en
     * Example of an ErrorInfo when access to Google Cloud Storage service is
     * blocked by the Google Workspace administrator:
     *     { "reason": "USER_BLOCKED_BY_ADMIN",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "storage.googleapis.com",
     *         "method": "google.storage.v1.Storage.GetObject",
     *       }
     *     }
     *
     * Generated from protobuf enum <code>USER_BLOCKED_BY_ADMIN = 24;</code>
     */
    const USER_BLOCKED_BY_ADMIN = 24;
    /**
     * The request is denied because the resource service usage is restricted
     * by administrators according to the organization policy constraint.
     * For more information see
     * https://cloud.google.com/resource-manager/docs/organization-policy/restricting-services.
     * Example of an ErrorInfo when access to Google Cloud Storage service is
     * restricted by Resource Usage Restriction policy:
     *     { "reason": "RESOURCE_USAGE_RESTRICTION_VIOLATED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/project-123",
     *         "service": "storage.googleapis.com"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>RESOURCE_USAGE_RESTRICTION_VIOLATED = 25;</code>
     */
    const RESOURCE_USAGE_RESTRICTION_VIOLATED = 25;
    /**
     * Unimplemented. Do not use.
     * The request is denied because it contains unsupported system parameters in
     * URL query parameters or HTTP headers. For more information,
     * see https://cloud.google.com/apis/docs/system-parameters
     * Example of an ErrorInfo when access "pubsub.googleapis.com" service with
     * a request header of "x-goog-user-ip":
     *     { "reason": "SYSTEM_PARAMETER_UNSUPPORTED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "service": "pubsub.googleapis.com"
     *         "parameter": "x-goog-user-ip"
     *       }
     *     }
     *
     * Generated from protobuf enum <code>SYSTEM_PARAMETER_UNSUPPORTED = 26;</code>
     */
    const SYSTEM_PARAMETER_UNSUPPORTED = 26;
    /**
     * The request is denied because it violates Org Restriction: the requested
     * resource does not belong to allowed organizations specified in
     * "X-Goog-Allowed-Resources" header.
     * Example of an ErrorInfo when accessing a GCP resource that is restricted by
     * Org Restriction for "pubsub.googleapis.com" service.
     * {
     *   reason: "ORG_RESTRICTION_VIOLATION"
     *   domain: "googleapis.com"
     *   metadata {
     *     "consumer":"projects/123456"
     *     "service": "pubsub.googleapis.com"
     *   }
     * }
     *
     * Generated from protobuf enum <code>ORG_RESTRICTION_VIOLATION = 27;</code>
     */
    const ORG_RESTRICTION_VIOLATION = 27;
    /**
     * The request is denied because "X-Goog-Allowed-Resources" header is in a bad
     * format.
     * Example of an ErrorInfo when
     * accessing "pubsub.googleapis.com" service with an invalid
     * "X-Goog-Allowed-Resources" request header.
     * {
     *   reason: "ORG_RESTRICTION_HEADER_INVALID"
     *   domain: "googleapis.com"
     *   metadata {
     *     "consumer":"projects/123456"
     *     "service": "pubsub.googleapis.com"
     *   }
     * }
     *
     * Generated from protobuf enum <code>ORG_RESTRICTION_HEADER_INVALID = 28;</code>
     */
    const ORG_RESTRICTION_HEADER_INVALID = 28;
    /**
     * Unimplemented. Do not use.
     * The request is calling a service that is not visible to the consumer.
     * Example of an ErrorInfo when the consumer "projects/123" contacting
     *  "pubsub.googleapis.com" service which is not visible to the consumer.
     *     { "reason": "SERVICE_NOT_VISIBLE",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "pubsub.googleapis.com"
     *       }
     *     }
     * This response indicates the "pubsub.googleapis.com" is not visible to
     * "projects/123" (or it may not exist).
     *
     * Generated from protobuf enum <code>SERVICE_NOT_VISIBLE = 29;</code>
     */
    const SERVICE_NOT_VISIBLE = 29;
    /**
     * The request is related to a project for which GCP access is suspended.
     * Example of an ErrorInfo when the consumer "projects/123" fails to contact
     * "pubsub.googleapis.com" service because GCP access is suspended:
     *     { "reason": "GCP_SUSPENDED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "pubsub.googleapis.com"
     *       }
     *     }
     * This response indicates the associated GCP account has been suspended.
     *
     * Generated from protobuf enum <code>GCP_SUSPENDED = 30;</code>
     */
    const GCP_SUSPENDED = 30;
    /**
     * The request violates the location policies when creating resources in
     * the restricted region.
     * Example of an ErrorInfo when creating the Cloud Storage Bucket by
     * "projects/123" for service storage.googleapis.com:
     *     { "reason": "LOCATION_POLICY_VIOLATED",
     *       "domain": "googleapis.com",
     *       "metadata": {
     *         "consumer": "projects/123",
     *         "service": "storage.googleapis.com",
     *       }
     *     }
     * This response indicates creating the Cloud Storage Bucket in
     * "locations/asia-northeast3" violates at least one location policy.
     * The troubleshooting guidance is provided in the Help links.
     *
     * Generated from protobuf enum <code>LOCATION_POLICY_VIOLATED = 31;</code>
     */
    const LOCATION_POLICY_VIOLATED = 31;
    /**
     * The request is denied because origin request header is missing.
     * Example of an ErrorInfo when
     * accessing "pubsub.googleapis.com" service with an empty "Origin" request
     * header.
     * {
     *   reason: "MISSING_ORIGIN"
     *   domain: "googleapis.com"
     *   metadata {
     *     "consumer":"projects/123456"
     *     "service": "pubsub.googleapis.com"
     *   }
     * }
     *
     * Generated from protobuf enum <code>MISSING_ORIGIN = 33;</code>
     */
    const MISSING_ORIGIN = 33;
    /**
     * The request is denied because the request contains more than one credential
     * type that are individually acceptable, but not together. The customer
     * should retry their request with only one set of credentials.
     * Example of an ErrorInfo when
     * accessing "pubsub.googleapis.com" service with overloaded credentials.
     * {
     *   reason: "OVERLOADED_CREDENTIALS"
     *   domain: "googleapis.com"
     *   metadata {
     *     "consumer":"projects/123456"
     *     "service": "pubsub.googleapis.com"
     *   }
     * }
     *
     * Generated from protobuf enum <code>OVERLOADED_CREDENTIALS = 34;</code>
     */
    const OVERLOADED_CREDENTIALS = 34;

    private static $valueToName = [
        self::ERROR_REASON_UNSPECIFIED => 'ERROR_REASON_UNSPECIFIED',
        self::SERVICE_DISABLED => 'SERVICE_DISABLED',
        self::BILLING_DISABLED => 'BILLING_DISABLED',
        self::API_KEY_INVALID => 'API_KEY_INVALID',
        self::API_KEY_SERVICE_BLOCKED => 'API_KEY_SERVICE_BLOCKED',
        self::API_KEY_HTTP_REFERRER_BLOCKED => 'API_KEY_HTTP_REFERRER_BLOCKED',
        self::API_KEY_IP_ADDRESS_BLOCKED => 'API_KEY_IP_ADDRESS_BLOCKED',
        self::API_KEY_ANDROID_APP_BLOCKED => 'API_KEY_ANDROID_APP_BLOCKED',
        self::API_KEY_IOS_APP_BLOCKED => 'API_KEY_IOS_APP_BLOCKED',
        self::RATE_LIMIT_EXCEEDED => 'RATE_LIMIT_EXCEEDED',
        self::RESOURCE_QUOTA_EXCEEDED => 'RESOURCE_QUOTA_EXCEEDED',
        self::LOCATION_TAX_POLICY_VIOLATED => 'LOCATION_TAX_POLICY_VIOLATED',
        self::USER_PROJECT_DENIED => 'USER_PROJECT_DENIED',
        self::CONSUMER_SUSPENDED => 'CONSUMER_SUSPENDED',
        self::CONSUMER_INVALID => 'CONSUMER_INVALID',
        self::SECURITY_POLICY_VIOLATED => 'SECURITY_POLICY_VIOLATED',
        self::ACCESS_TOKEN_EXPIRED => 'ACCESS_TOKEN_EXPIRED',
        self::ACCESS_TOKEN_SCOPE_INSUFFICIENT => 'ACCESS_TOKEN_SCOPE_INSUFFICIENT',
        self::ACCOUNT_STATE_INVALID => 'ACCOUNT_STATE_INVALID',
        self::ACCESS_TOKEN_TYPE_UNSUPPORTED => 'ACCESS_TOKEN_TYPE_UNSUPPORTED',
        self::CREDENTIALS_MISSING => 'CREDENTIALS_MISSING',
        self::RESOURCE_PROJECT_INVALID => 'RESOURCE_PROJECT_INVALID',
        self::SESSION_COOKIE_INVALID => 'SESSION_COOKIE_INVALID',
        self::USER_BLOCKED_BY_ADMIN => 'USER_BLOCKED_BY_ADMIN',
        self::RESOURCE_USAGE_RESTRICTION_VIOLATED => 'RESOURCE_USAGE_RESTRICTION_VIOLATED',
        self::SYSTEM_PARAMETER_UNSUPPORTED => 'SYSTEM_PARAMETER_UNSUPPORTED',
        self::ORG_RESTRICTION_VIOLATION => 'ORG_RESTRICTION_VIOLATION',
        self::ORG_RESTRICTION_HEADER_INVALID => 'ORG_RESTRICTION_HEADER_INVALID',
        self::SERVICE_NOT_VISIBLE => 'SERVICE_NOT_VISIBLE',
        self::GCP_SUSPENDED => 'GCP_SUSPENDED',
        self::LOCATION_POLICY_VIOLATED => 'LOCATION_POLICY_VIOLATED',
        self::MISSING_ORIGIN => 'MISSING_ORIGIN',
        self::OVERLOADED_CREDENTIALS => 'OVERLOADED_CREDENTIALS',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

